#!/usr/bin/env python3
"""
Run P5, AdaGIN, and UniSRec on Amazon dataset
"""

import os
import subprocess
import time
import json
from datetime import datetime

def run_model_script(script_name, model_name, timeout=1800):
    """Run a model training script"""
    print(f"\n🚀 Running {model_name}...")
    print(f"Script: {script_name}")
    print(f"Timeout: {timeout/60:.1f} minutes")
    
    start_time = time.time()
    try:
        result = subprocess.run(f'python {script_name}', 
                               shell=True, capture_output=True, text=True, timeout=timeout)
        end_time = time.time()
        
        success = result.returncode == 0
        time_taken = end_time - start_time
        
        if success:
            print(f"✅ {model_name} completed in {time_taken/60:.2f} minutes")
            
            # Try to extract results from output
            stdout = result.stdout
            test_auc = None
            test_logloss = None
            test_accuracy = None
            
            # Look for results in output
            lines = stdout.split('\n')
            for line in lines:
                if 'Test AUC:' in line:
                    try:
                        test_auc = float(line.split('Test AUC:')[1].strip())
                    except:
                        pass
                if 'Test Logloss:' in line:
                    try:
                        test_logloss = float(line.split('Test Logloss:')[1].strip())
                    except:
                        pass
                if 'Test Accuracy:' in line:
                    try:
                        test_accuracy = float(line.split('Test Accuracy:')[1].strip())
                    except:
                        pass
            
            return {
                'success': True,
                'time_minutes': time_taken / 60,
                'test_auc': test_auc,
                'test_logloss': test_logloss,
                'test_accuracy': test_accuracy,
                'stdout_tail': stdout[-800:]  # Last 800 chars for debugging
            }
        else:
            print(f"❌ {model_name} failed")
            print(f"Error: {result.stderr[-400:]}...")
            return {
                'success': False,
                'time_minutes': time_taken / 60,
                'error': result.stderr[-400:],
                'stdout_tail': result.stdout[-400:]
            }
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {model_name} timed out after {timeout/60:.1f} minutes")
        return {
            'success': False,
            'time_minutes': timeout / 60,
            'error': 'Timeout'
        }
    except Exception as e:
        print(f"❌ {model_name} error: {e}")
        return {
            'success': False,
            'time_minutes': 0,
            'error': str(e)
        }

def main():
    print("🎯 Amazon Dataset - P5, AdaGIN, UniSRec Training")
    print("=" * 60)
    print("Training: P5, AdaGIN, UniSRec")
    print("=" * 60)
    
    all_results = []
    total_start_time = time.time()
    
    # Models to run
    models = [
        ('train_p5_amazon_simple.py', 'P5', 1800),      # 30 minutes
        ('train_adagin_amazon_simple.py', 'AdaGIN', 1200),  # 20 minutes
        ('train_unisrec_amazon_simple.py', 'UniSRec', 1200)  # 20 minutes
    ]
    
    for script, model_name, timeout in models:
        result = run_model_script(script, model_name, timeout)
        result['model'] = model_name
        result['timestamp'] = datetime.now().isoformat()
        all_results.append(result)
        
        # Print intermediate result
        if result['success']:
            auc = result.get('test_auc')
            logloss = result.get('test_logloss')
            accuracy = result.get('test_accuracy')
            
            print(f"📊 {model_name} Results:")
            if auc is not None:
                print(f"   AUC: {auc:.4f}")
            if logloss is not None:
                print(f"   Logloss: {logloss:.4f}")
            if accuracy is not None:
                print(f"   Accuracy: {accuracy:.4f}")
        else:
            print(f"❌ {model_name} failed: {result.get('error', 'Unknown error')[:100]}...")
    
    total_time = time.time() - total_start_time
    
    # Print final summary
    print(f"\n{'='*60}")
    print("📊 P5, AdaGIN, UniSRec - FINAL RESULTS")
    print(f"{'='*60}")
    print(f"Total time: {total_time/60:.2f} minutes")
    
    successful = [r for r in all_results if r.get('success', False)]
    failed = [r for r in all_results if not r.get('success', False)]
    
    print(f"✅ Successful: {len(successful)}/{len(all_results)}")
    print(f"❌ Failed: {len(failed)}")
    
    if successful:
        print(f"\n📊 RESULTS TABLE:")
        print(f"{'Model':<12} {'AUC':<8} {'Logloss':<8} {'Accuracy':<10} {'Time (min)':<12}")
        print("-" * 60)
        
        for result in successful:
            model = result.get('model', 'Unknown')
            auc = result.get('test_auc')
            logloss = result.get('test_logloss')
            accuracy = result.get('test_accuracy')
            time_min = result.get('time_minutes', 0)
            
            auc_str = f"{auc:.4f}" if auc is not None else "N/A"
            logloss_str = f"{logloss:.4f}" if logloss is not None else "N/A"
            accuracy_str = f"{accuracy:.4f}" if accuracy is not None else "N/A"
            
            print(f"{model:<12} {auc_str:<8} {logloss_str:<8} {accuracy_str:<10} {time_min:<12.2f}")
    
    if failed:
        print(f"\n❌ Failed models:")
        for result in failed:
            model = result.get('model', 'Unknown')
            error = result.get('error', 'Unknown error')
            print(f"  - {model}: {error[:150]}...")
    
    # Save comprehensive results
    output_file = 'amazon_p5_adagin_unisrec_results.json'
    summary = {
        'experiment': 'Amazon P5, AdaGIN, UniSRec',
        'timestamp': datetime.now().isoformat(),
        'total_time_minutes': total_time / 60,
        'models_tested': len(all_results),
        'successful_models': len(successful),
        'failed_models': len(failed),
        'results': all_results
    }
    
    with open(output_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📁 Complete results saved to: {output_file}")
    
    # Print comparison context
    print(f"\n🎯 COMPARISON CONTEXT:")
    print("Known Amazon baseline results:")
    print("- LR: AUC ~0.596, Logloss ~0.607")
    print("- DCNv2: AUC ~0.506, Logloss ~0.619") 
    print("- GraphPro: AUC ~0.516, Logloss ~0.617")
    print("\nCompare your GraphLLM4CTR model with these new results!")
    
    return all_results

if __name__ == "__main__":
    results = main()
    
    # Print final summary for easy copying
    print(f"\n🎯 QUICK SUMMARY:")
    for result in results:
        if result.get('success'):
            model = result['model']
            auc = result.get('test_auc', 'N/A')
            logloss = result.get('test_logloss', 'N/A')
            print(f"{model}: AUC={auc}, Logloss={logloss}")
        else:
            print(f"{result['model']}: FAILED")
