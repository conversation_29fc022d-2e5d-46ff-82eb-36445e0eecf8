{"experiment": "Amazon P5, AdaGIN, UniSRec", "timestamp": "2025-05-29T05:54:29.797720", "total_time_minutes": 2.4226761142412823, "models_tested": 3, "successful_models": 1, "failed_models": 2, "results": [{"success": false, "time_minutes": 1.4315338015556336, "error": "/python3.8/json/encoder.py\", line 405, in _iterencode_dict\n    yield from chunks\n  File \"/root/miniconda3/envs/py38/lib/python3.8/json/encoder.py\", line 438, in _iterencode\n    o = _default(o)\n  File \"/root/miniconda3/envs/py38/lib/python3.8/json/encoder.py\", line 179, in default\n    raise TypeError(f'Object of type {o.__class__.__name__} '\nTypeError: Object of type int64 is not JSON serializable\n", "stdout_tail": "🚀 Starting P5 training on Amazon dataset...\n📊 Preparing Amazon data for P5...\n", "model": "P5", "timestamp": "2025-05-29T05:53:30.329172"}, {"success": true, "time_minutes": 0.14959510564804077, "test_auc": null, "test_logloss": null, "test_accuracy": null, "stdout_tail": " 'price_range_index': {'vocab_size': 6, 'max_value': 6}, 'category_1': {'vocab_size': 1, 'max_value': 32}, 'category_2': {'vocab_size': 3, 'max_value': 31}, 'category_3': {'vocab_size': 40, 'max_value': 43}}\n❌ AdaGIN training failed!\nTraceback (most recent call last):\n  File \"run_amazon_adagin.py\", line 90, in <module>\n    with open('GraphLLM4CTR_baseline/AutoGIM/data/amazon/feature_vocab.json', 'r') as f:\nFileNotFoundError: [Errno 2] No such file or directory: 'GraphLLM4CTR_baseline/AutoGIM/data/amazon/feature_vocab.json'\n\nAdaGIN Result: {'model': 'AdaGIN', 'status': 'failed', 'error': \" open('GraphLLM4CTR_baseline/AutoGIM/data/amazon/feature_vocab.json', 'r') as f:\\nFileNotFoundError: [Errno 2] No such file or directory: 'GraphLLM4CTR_baseline/AutoGIM/data/amazon/feature_vocab.json'\\n\"}\n", "model": "AdaGIN", "timestamp": "2025-05-29T05:53:39.305031"}, {"success": false, "time_minutes": 0.841541035970052, "error": "ne 3772, in to_csv\n    return <PERSON><PERSON>rame<PERSON><PERSON><PERSON>(formatter).to_csv(\n  File \"/root/miniconda3/envs/py38/lib/python3.8/site-packages/pandas/io/formats/format.py\", line 1186, in to_csv\n    csv_formatter.save()\n  File \"/root/miniconda3/envs/py38/lib/python3.8/site-packages/pandas/io/formats/csvs.py\", line 249, in save\n    self.writer = csvlib.writer(\nTypeError: \"delimiter\" must be a 1-character string\n", "stdout_tail": "🚀 Starting UniSRec training on Amazon dataset...\n📊 Preparing Amazon data for UniSRec...\n", "model": "UniSRec", "timestamp": "2025-05-29T05:54:29.797600"}]}