#!/usr/bin/env python3
"""
Run all baseline models on Amazon dataset including P5, AdaGIN, and UniSRec
"""

import os
import subprocess
import time
import json
from datetime import datetime

def run_model_script(script_name, model_name, timeout=1800):
    """Run a model training script"""
    print(f"\n🚀 Running {model_name}...")
    print(f"Script: {script_name}")
    
    start_time = time.time()
    try:
        result = subprocess.run(f'python {script_name}', 
                               shell=True, capture_output=True, text=True, timeout=timeout)
        end_time = time.time()
        
        success = result.returncode == 0
        time_taken = end_time - start_time
        
        if success:
            print(f"✅ {model_name} completed in {time_taken/60:.2f} minutes")
            
            # Try to extract results from output
            stdout = result.stdout
            test_auc = None
            test_logloss = None
            
            # Look for results in output
            lines = stdout.split('\\n')
            for line in lines:
                if 'Test AUC:' in line:
                    try:
                        test_auc = float(line.split('Test AUC:')[1].strip())
                    except:
                        pass
                if 'Test Logloss:' in line:
                    try:
                        test_logloss = float(line.split('Test Logloss:')[1].strip())
                    except:
                        pass
            
            return {
                'success': True,
                'time_minutes': time_taken / 60,
                'test_auc': test_auc,
                'test_logloss': test_logloss,
                'stdout_tail': stdout[-500:]  # Last 500 chars
            }
        else:
            print(f"❌ {model_name} failed")
            print(f"Error: {result.stderr[-300:]}...")
            return {
                'success': False,
                'time_minutes': time_taken / 60,
                'error': result.stderr[-300:]
            }
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {model_name} timed out after {timeout/60:.1f} minutes")
        return {
            'success': False,
            'time_minutes': timeout / 60,
            'error': 'Timeout'
        }
    except Exception as e:
        print(f"❌ {model_name} error: {e}")
        return {
            'success': False,
            'time_minutes': 0,
            'error': str(e)
        }

def run_existing_models():
    """Run the existing working models"""
    existing_results = []
    
    # # DCNv2
    # print("\\n🚀 Running DCNv2...")
    # try:
    #     result = subprocess.run('cd GraphLLM4CTR_baseline/DCNv2 && python train_amazon.py --num_epochs 3', 
    #                            shell=True, capture_output=True, text=True, timeout=600)
    #     if result.returncode == 0:
    #         print("✅ DCNv2 completed")
    #         existing_results.append({'model': 'DCNv2', 'status': 'success'})
    #     else:
    #         print("❌ DCNv2 failed")
    #         existing_results.append({'model': 'DCNv2', 'status': 'failed'})
    # except:
    #     existing_results.append({'model': 'DCNv2', 'status': 'error'})
    
    # # GraphPro
    # print("\\n🚀 Running GraphPro...")
    # try:
    #     result = subprocess.run('cd GraphLLM4CTR_baseline/GraphPro && python train_amazon_graphpro.py --num_epochs 3', 
    #                            shell=True, capture_output=True, text=True, timeout=900)
    #     if result.returncode == 0:
    #         print("✅ GraphPro completed")
    #         existing_results.append({'model': 'GraphPro', 'status': 'success'})
    #     else:
    #         print("❌ GraphPro failed")
    #         existing_results.append({'model': 'GraphPro', 'status': 'failed'})
    # except:
    #     existing_results.append({'model': 'GraphPro', 'status': 'error'})
    
    # return existing_results

def main():
    print("🎯 Amazon Dataset - All Baseline Models Training")
    print("=" * 60)
    print("Training: DCNv2, GraphPro, P5, AdaGIN, UniSRec")
    print("=" * 60)
    
    all_results = []
    total_start_time = time.time()
    
    # Run existing working models
    print("\\n📊 PHASE 1: Running existing working models...")
    existing_results = run_existing_models()
    all_results.extend(existing_results)
    
    # Run new models
    print("\\n📊 PHASE 2: Running P5, AdaGIN, UniSRec...")
    
    new_models = [
        ('train_p5_amazon_simple.py', 'P5', 1800),
        ('train_adagin_amazon_simple.py', 'AdaGIN', 1200),
        ('train_unisrec_amazon_simple.py', 'UniSRec', 1200)
    ]
    
    for script, model_name, timeout in new_models:
        result = run_model_script(script, model_name, timeout)
        result['model'] = model_name
        result['timestamp'] = datetime.now().isoformat()
        all_results.append(result)
    
    total_time = time.time() - total_start_time
    
    # Print summary
    print(f"\\n{'='*60}")
    print("📊 AMAZON BASELINE MODELS - FINAL RESULTS")
    print(f"{'='*60}")
    print(f"Total time: {total_time/60:.2f} minutes")
    
    successful = [r for r in all_results if r.get('success', False) or r.get('status') == 'success']
    failed = [r for r in all_results if not (r.get('success', False) or r.get('status') == 'success')]
    
    print(f"✅ Successful: {len(successful)}/{len(all_results)}")
    print(f"❌ Failed: {len(failed)}")
    
    if successful:
        print(f"\\n📊 RESULTS TABLE:")
        print(f"{'Model':<12} {'AUC':<8} {'Logloss':<8} {'Status':<12}")
        print("-" * 45)
        
        for result in successful:
            model = result.get('model', 'Unknown')
            auc = result.get('test_auc')
            logloss = result.get('test_logloss')
            status = result.get('status', 'success')
            
            auc_str = f"{auc:.4f}" if auc is not None else "N/A"
            logloss_str = f"{logloss:.4f}" if logloss is not None else "N/A"
            
            print(f"{model:<12} {auc_str:<8} {logloss_str:<8} {status:<12}")
    
    if failed:
        print(f"\\n❌ Failed models:")
        for result in failed:
            model = result.get('model', 'Unknown')
            error = result.get('error', 'Unknown error')
            print(f"  - {model}: {error[:100]}...")
    
    # Save comprehensive results
    output_file = 'amazon_all_baselines_results.json'
    summary = {
        'experiment': 'Amazon All Baseline Models',
        'timestamp': datetime.now().isoformat(),
        'total_time_minutes': total_time / 60,
        'models_tested': len(all_results),
        'successful_models': len(successful),
        'failed_models': len(failed),
        'results': all_results
    }
    
    with open(output_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\\n📁 Complete results saved to: {output_file}")
    
    # Print comparison with known baselines
    print(f"\\n🎯 COMPARISON WITH KNOWN BASELINES:")
    print("- LR (from ctr-metrics-eval): AUC ~0.596")
    print("- DCNv2: AUC ~0.506") 
    print("- GraphPro: AUC ~0.516")
    print("\\nCompare your GraphLLM4CTR model with these results!")

if __name__ == "__main__":
    main()
