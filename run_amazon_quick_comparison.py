#!/usr/bin/env python3
"""
Quick comparison of working baseline models on Amazon dataset
"""

import os
import subprocess
import time
import json
from datetime import datetime

def run_model(name, command, timeout=900):
    """Run a single model training"""
    print(f"\n🚀 Training {name}...")
    print(f"Command: {command}")
    
    start_time = time.time()
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=timeout)
        end_time = time.time()
        
        success = result.returncode == 0
        time_taken = end_time - start_time
        
        if success:
            print(f"✅ {name} completed in {time_taken/60:.2f} minutes")
            
            # Try to extract metrics from output
            stdout = result.stdout
            test_auc = None
            test_logloss = None
            
            # Look for metrics in output
            lines = stdout.split('\n')
            for line in lines:
                if 'Test AUC' in line and 'Test LogLoss' in line:
                    try:
                        # Parse line like: "Test AUC: 0.5161 - Test LogLoss: 0.6166"
                        parts = line.split(' - ')
                        for part in parts:
                            if 'Test AUC' in part:
                                test_auc = float(part.split(':')[1].strip())
                            elif 'Test LogLoss' in part:
                                test_logloss = float(part.split(':')[1].strip())
                    except:
                        pass
                elif 'Final test metrics' in line:
                    try:
                        # Parse line like: "Final test metrics - AUC: 0.5161, LogLoss: 0.6166"
                        if 'AUC:' in line:
                            test_auc = float(line.split('AUC:')[1].split(',')[0].strip())
                        if 'LogLoss:' in line:
                            test_logloss = float(line.split('LogLoss:')[1].split(',')[0].strip())
                    except:
                        pass
            
            return {
                'success': True,
                'time_minutes': time_taken / 60,
                'test_auc': test_auc,
                'test_logloss': test_logloss,
                'stdout': stdout[-1000:]  # Last 1000 chars for debugging
            }
        else:
            print(f"❌ {name} failed")
            print(f"Error: {result.stderr[:200]}...")
            return {
                'success': False,
                'time_minutes': time_taken / 60,
                'error': result.stderr[:200]
            }
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {name} timed out after {timeout/60:.1f} minutes")
        return {
            'success': False,
            'time_minutes': timeout / 60,
            'error': 'Timeout'
        }
    except Exception as e:
        print(f"❌ {name} error: {e}")
        return {
            'success': False,
            'time_minutes': 0,
            'error': str(e)
        }

def main():
    print("🎯 Amazon Dataset Quick Baseline Comparison")
    print("=" * 60)
    
    # Working models to test
    models = [
        {
            'name': 'DCNv2',
            'command': 'cd GraphLLM4CTR_baseline/DCNv2 && python train_amazon.py --num_epochs 3',
            'timeout': 600
        },
        {
            'name': 'GraphPro',
            'command': 'cd GraphLLM4CTR_baseline/GraphPro && python train_amazon_graphpro.py --num_epochs 3',
            'timeout': 900
        },
        {
            'name': 'LR',
            'command': 'cd ctr-metrics-eval && python scripts/train.py --dataset_name AmazonCTR --dataset_path /data/datasets/processed_datasets/amazon --model_name LR --model_output_path output_amazon/ --epochs 3 --batch_size 256 --embedding_dim 16 --learning_rate 0.001 --loss binary_crossentropy --optimizer adam --monitor_mode max --patience 2 --regularizer 0.00012 --task binary_classification --every_x_epochs 1 --seed 2023',
            'timeout': 600
        },
        {
            'name': 'FM',
            'command': 'cd ctr-metrics-eval && python scripts/train.py --dataset_name AmazonCTR --dataset_path /data/datasets/processed_datasets/amazon --model_name FM --model_output_path output_amazon/ --epochs 3 --batch_size 256 --embedding_dim 16 --learning_rate 0.001 --loss binary_crossentropy --optimizer adam --monitor_mode max --patience 2 --regularizer 0.00012 --task binary_classification --every_x_epochs 1 --seed 2023',
            'timeout': 600
        },
        {
            'name': 'DeepFM',
            'command': 'cd ctr-metrics-eval && python scripts/train.py --dataset_name AmazonCTR --dataset_path /data/datasets/processed_datasets/amazon --model_name DeepFM --model_output_path output_amazon/ --epochs 3 --batch_size 256 --embedding_dim 16 --learning_rate 0.001 --loss binary_crossentropy --optimizer adam --monitor_mode max --patience 2 --regularizer 0.00012 --task binary_classification --every_x_epochs 1 --seed 2023',
            'timeout': 900
        }
    ]
    
    results = []
    total_start_time = time.time()
    
    # Run each model
    for model in models:
        result = run_model(model['name'], model['command'], model['timeout'])
        result['model'] = model['name']
        result['timestamp'] = datetime.now().isoformat()
        results.append(result)
    
    total_time = time.time() - total_start_time
    
    # Print summary
    print(f"\n{'='*60}")
    print("📊 AMAZON BASELINE COMPARISON RESULTS")
    print(f"{'='*60}")
    print(f"Total time: {total_time/60:.2f} minutes")
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}")
    
    if successful:
        print(f"\n📊 RESULTS TABLE:")
        print(f"{'Model':<12} {'AUC':<8} {'Logloss':<8} {'Time (min)':<12}")
        print("-" * 45)
        
        for result in successful:
            auc = result.get('test_auc')
            logloss = result.get('test_logloss')
            time_min = result['time_minutes']
            
            auc_str = f"{auc:.4f}" if auc is not None else "N/A"
            logloss_str = f"{logloss:.4f}" if logloss is not None else "N/A"
            
            print(f"{result['model']:<12} {auc_str:<8} {logloss_str:<8} {time_min:<12.2f}")
    
    if failed:
        print(f"\n❌ Failed models:")
        for result in failed:
            print(f"  - {result['model']}: {result.get('error', 'Unknown error')}")
    
    # Save results
    output_file = 'amazon_quick_comparison_results.json'
    summary = {
        'experiment': 'Amazon Quick Baseline Comparison',
        'timestamp': datetime.now().isoformat(),
        'total_time_minutes': total_time / 60,
        'successful_models': len(successful),
        'failed_models': len(failed),
        'results': results
    }
    
    with open(output_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📁 Results saved to: {output_file}")
    
    # Print next steps
    print(f"\n🎯 NEXT STEPS:")
    print("1. Compare these results with your GraphLLM4CTR model")
    print("2. For P5, AdaGIN, UniSRec - adapt the existing BookCrossing scripts")
    print("3. Run full comparative study with all models")

if __name__ == "__main__":
    main()
