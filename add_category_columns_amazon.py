import pandas as pd
import os
import ast

def process_category_indices(category_indices_str):
    """Convert category_indices string to separate columns"""
    try:
        # Parse the string representation of list
        if pd.isna(category_indices_str) or category_indices_str == '':
            return [0, 0, 0]
        
        # Handle different string formats
        category_indices_str = str(category_indices_str).strip()
        if category_indices_str.startswith('[') and category_indices_str.endswith(']'):
            # Parse as list
            category_list = ast.literal_eval(category_indices_str)
        else:
            # Handle other formats
            category_list = [0, 0, 0]
        
        # Ensure we have exactly 3 elements
        while len(category_list) < 3:
            category_list.append(0)
        
        return category_list[:3]
    except:
        return [0, 0, 0]

def add_category_columns_to_file(input_file, output_file):
    """Add category_1, category_2, category_3 columns to a CSV file"""
    print(f"Processing {input_file}...")
    
    # Read the file
    df = pd.read_csv(input_file)
    print(f"Original shape: {df.shape}")
    print(f"Original columns: {list(df.columns)}")
    
    # Check if category_indices column exists
    if 'category_indices' not in df.columns:
        print("Warning: category_indices column not found!")
        return
    
    # Process category_indices to create separate columns
    print("Processing category_indices...")
    category_data = df['category_indices'].apply(process_category_indices)
    
    # Add new columns
    df['category_1'] = category_data.apply(lambda x: x[0])
    df['category_2'] = category_data.apply(lambda x: x[1])
    df['category_3'] = category_data.apply(lambda x: x[2])
    
    print(f"New shape: {df.shape}")
    print(f"New columns: {list(df.columns)}")
    
    # Show sample of new columns
    print("\nSample of new category columns:")
    print(df[['category_indices', 'category_1', 'category_2', 'category_3']].head())
    
    # Save to new file
    df.to_csv(output_file, index=False)
    print(f"Saved to {output_file}")

def main():
    # Define paths
    data_dir = "/data/datasets/processed_datasets/amazon"
    
    # Process each file
    files_to_process = ['train.csv', 'val.csv', 'test.csv']
    
    for filename in files_to_process:
        input_file = os.path.join(data_dir, filename)
        output_file = os.path.join(data_dir, f"{filename.replace('.csv', '_with_categories.csv')}")
        
        if os.path.exists(input_file):
            add_category_columns_to_file(input_file, output_file)
            print(f"✅ Processed {filename}")
        else:
            print(f"❌ File not found: {input_file}")
        print("-" * 50)

if __name__ == "__main__":
    main()
    print("\n✅ All files processed successfully!")
    print("New files created:")
    print("  - train_with_categories.csv")
    print("  - val_with_categories.csv") 
    print("  - test_with_categories.csv")
