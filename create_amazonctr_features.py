#!/usr/bin/env python3
"""
Scrip<PERSON> to create feature_encoder.pkl and feature_map.json for AmazonCTR dataset
This creates features for the dataset with category_1, category_2, category_3 columns
"""

import sys
import os
import logging

# Add the ctr-metrics-eval src to path
sys.path.append('/root/code/ctr-metrics-eval/src')

from data_process.data_processor import DataProcessor

def create_amazonctr_features():
    """Create the AmazonCTR dataset feature files"""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(message)s'
    )
    
    print("🔄 Creating AmazonCTR dataset feature files...")
    print("📁 Dataset path: /data/datasets/processed_datasets/amazon")
    print("📊 Using files: train_new.csv, val_new.csv, test_new.csv")
    
    # Create processor for AmazonCTR dataset
    processor = DataProcessor(
        dataset_name='AmazonCTR',
        data_dir='/data/datasets/processed_datasets/amazon',
        model_name='temp',
        model_output_path='temp_output'
    )
    
    print("📊 Processing features from train_new.csv...")
    
    # Generate the feature encoder and feature map for AmazonCTR
    processor.process_encoder_map()
    
    # Check if files were created
    feature_encoder_path = '/data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl'
    feature_map_path = '/data/datasets/processed_datasets/amazon/feature_map_amazonctr.json'
    
    # The processor saves with default names, so we need to rename them
    default_encoder = '/data/datasets/processed_datasets/amazon/feature_encoder.pkl'
    default_map = '/data/datasets/processed_datasets/amazon/feature_map.json'
    
    if os.path.exists(default_encoder) and os.path.exists(default_map):
        # Rename to AmazonCTR specific names
        os.rename(default_encoder, feature_encoder_path)
        os.rename(default_map, feature_map_path)
        
        print("✅ Successfully created AmazonCTR feature files:")
        print(f"   📄 {feature_encoder_path}")
        print(f"   📄 {feature_map_path}")
        
        # Show file sizes
        encoder_size = os.path.getsize(feature_encoder_path)
        map_size = os.path.getsize(feature_map_path)
        print(f"📏 File sizes:")
        print(f"   feature_encoder_amazonctr.pkl: {encoder_size:,} bytes")
        print(f"   feature_map_amazonctr.json: {map_size:,} bytes")
        
    else:
        print("❌ Failed to create AmazonCTR feature files!")
        return False
    
    print("\n🎯 These files are ready for:")
    print("   • Comparative study with split category features")
    print("   • CTR model training with category_1, category_2, category_3")
    
    return True

if __name__ == "__main__":
    success = create_amazonctr_features()
    if success:
        print("\n🎉 AmazonCTR feature creation completed successfully!")
    else:
        print("\n💥 AmazonCTR feature creation failed!")
        sys.exit(1)
