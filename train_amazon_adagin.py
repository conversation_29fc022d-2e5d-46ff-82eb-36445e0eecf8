#!/usr/bin/env python3
"""
Train AdaGIN model on Amazon dataset for comparative study
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime

# Add AutoGIM to path
sys.path.append('GraphLLM4CTR_baseline/AutoGIM')

def prepare_amazon_data_for_adagin():
    """Prepare Amazon data for AdaGIN training"""
    print("📊 Preparing Amazon data for AdaGIN...")
    
    # Load the Amazon data with split categories
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')
    
    print(f"Train shape: {train_df.shape}")
    print(f"Val shape: {val_df.shape}")
    print(f"Test shape: {test_df.shape}")
    
    # Create output directory
    output_dir = 'GraphLLM4CTR_baseline/AutoGIM/Amazon_custom'
    os.makedirs(output_dir, exist_ok=True)
    
    # AdaGIN expects specific format similar to MovieLens
    # Use the same feature processing as in the AutoGIM baseline
    
    feature_columns = ['user_id', 'item_id', 'brand_index', 'price_range_index', 
                      'category_1', 'category_2', 'category_3', 'price']
    
    # Ensure all feature columns are present
    for col in feature_columns:
        if col not in train_df.columns:
            print(f"❌ Missing column: {col}")
            return None
    
    # Save in AdaGIN format
    train_df[feature_columns + ['label']].to_csv(f'{output_dir}/train.csv', index=False)
    val_df[feature_columns + ['label']].to_csv(f'{output_dir}/valid.csv', index=False)
    test_df[feature_columns + ['label']].to_csv(f'{output_dir}/test.csv', index=False)
    
    # Create feature vocabulary for AdaGIN
    feature_vocab = {}
    for col in feature_columns:
        if col != 'price':  # Skip numeric features
            unique_values = pd.concat([train_df[col], val_df[col], test_df[col]]).unique()
            feature_vocab[col] = {
                'vocab_size': len(unique_values),
                'values': unique_values.tolist()
            }
    
    with open(f'{output_dir}/feature_vocab.json', 'w') as f:
        json.dump(feature_vocab, f, indent=2)
    
    print(f"✅ AdaGIN data saved to {output_dir}")
    print(f"📊 Feature vocab sizes: {[(k, v['vocab_size']) for k, v in feature_vocab.items()]}")
    
    return output_dir

def train_amazon_adagin():
    """Train AdaGIN on Amazon dataset"""
    print("🚀 Starting AdaGIN training on Amazon dataset...")
    
    # Prepare data
    data_dir = prepare_amazon_data_for_adagin()
    if data_dir is None:
        print("❌ Data preparation failed!")
        return
    
    # Import AdaGIN modules
    try:
        from GraphLLM4CTR_baseline.AutoGIM.train_adagin import train_adagin_model
        from GraphLLM4CTR_baseline.AutoGIM.feature_embedding import create_feature_processor
    except ImportError as e:
        print(f"❌ Failed to import AdaGIN modules: {e}")
        return None
    
    # Configure AdaGIN parameters
    config = {
        'dataset_name': 'Amazon_custom',
        'data_dir': data_dir,
        'output_dir': 'GraphLLM4CTR_baseline/AutoGIM/output_amazon_adagin',
        'epochs': 10,
        'batch_size': 1024,
        'learning_rate': 0.001,
        'embedding_dim': 16,
        'hidden_dims': [256, 128],
        'dropout': 0.2,
        'seed': 2023,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu'
    }
    
    # Train the model
    try:
        # Create feature processor
        processor = create_feature_processor(config)
        
        # Train AdaGIN model
        results = train_adagin_model(config, processor)
        
        # Save results
        output_dir = config['output_dir']
        os.makedirs(output_dir, exist_ok=True)
        
        results_dict = {
            'model': 'AdaGIN',
            'dataset': 'Amazon',
            'timestamp': datetime.now().isoformat(),
            'test_auc': results.get('test_auc', 0.0),
            'test_logloss': results.get('test_logloss', 0.0),
            'test_accuracy': results.get('test_accuracy', 0.0),
            'config': config
        }
        
        with open(f'{output_dir}/results.json', 'w') as f:
            json.dump(results_dict, f, indent=2)
        
        print("✅ AdaGIN training completed!")
        print(f"📊 Results: AUC={results_dict['test_auc']:.4f}, "
              f"Logloss={results_dict['test_logloss']:.4f}")
        
        return results_dict
        
    except Exception as e:
        print(f"❌ AdaGIN training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    train_amazon_adagin()
