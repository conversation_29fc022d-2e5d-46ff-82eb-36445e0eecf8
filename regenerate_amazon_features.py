#!/usr/bin/env python3
"""
Script to regenerate feature_encoder.pkl and feature_map.json for Amazon dataset
This will create exactly the same files as before since the process is deterministic.
"""

import sys
import os
import logging

# Add the ctr-metrics-eval src to path
sys.path.append('/root/code/ctr-metrics-eval/src')

from data_process.data_processor import DataProcessor

def regenerate_amazon_features():
    """Regenerate the Amazon dataset feature files"""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(message)s'
    )
    
    print("🔄 Regenerating Amazon dataset feature files...")
    print("📁 Dataset path: /data/datasets/processed_datasets/amazon")
    
    # Create processor for original Amazon dataset
    processor = DataProcessor(
        dataset_name='Amazon',
        data_dir='/data/datasets/processed_datasets/amazon',
        model_name='temp',
        model_output_path='temp_output'
    )
    
    print("📊 Processing features from train.csv...")
    
    # Generate the feature encoder and feature map
    processor.process_encoder_map()
    
    # Check if files were created
    feature_encoder_path = '/data/datasets/processed_datasets/amazon/feature_encoder.pkl'
    feature_map_path = '/data/datasets/processed_datasets/amazon/feature_map.json'
    
    if os.path.exists(feature_encoder_path) and os.path.exists(feature_map_path):
        print("✅ Successfully regenerated:")
        print(f"   📄 {feature_encoder_path}")
        print(f"   📄 {feature_map_path}")
        
        # Show file sizes
        encoder_size = os.path.getsize(feature_encoder_path)
        map_size = os.path.getsize(feature_map_path)
        print(f"📏 File sizes:")
        print(f"   feature_encoder.pkl: {encoder_size:,} bytes")
        print(f"   feature_map.json: {map_size:,} bytes")
        
    else:
        print("❌ Failed to create feature files!")
        return False
    
    print("\n🎯 These files are now ready for:")
    print("   • Your GraphLLM4CTR research")
    print("   • Graph embedding generation") 
    print("   • CTR model training")
    print("   • Any other experiments using Amazon dataset")
    
    return True

if __name__ == "__main__":
    success = regenerate_amazon_features()
    if success:
        print("\n🎉 Feature regeneration completed successfully!")
    else:
        print("\n💥 Feature regeneration failed!")
        sys.exit(1)
