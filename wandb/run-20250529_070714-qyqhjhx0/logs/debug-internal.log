{"time":"2025-05-29T07:07:14.746446818Z","level":"INFO","msg":"stream: starting","core version":"0.19.10","symlink path":"/root/code/wandb/run-20250529_070714-qyqhjhx0/logs/debug-core.log"}
{"time":"2025-05-29T07:07:15.459887346Z","level":"INFO","msg":"created new stream","id":"qyqhjhx0"}
{"time":"2025-05-29T07:07:15.459933993Z","level":"INFO","msg":"stream: started","id":"qyqhjhx0"}
{"time":"2025-05-29T07:07:15.459951053Z","level":"INFO","msg":"writer: Do: started","stream_id":"qyqhjhx0"}
{"time":"2025-05-29T07:07:15.459994623Z","level":"INFO","msg":"handler: started","stream_id":"qyqhjhx0"}
{"time":"2025-05-29T07:07:15.460037243Z","level":"INFO","msg":"sender: started","stream_id":"qyqhjhx0"}
{"time":"2025-05-29T07:07:15.84753993Z","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-29T07:14:23.653719127Z","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-29T07:14:23.653777644Z","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-29T07:14:24.654986651Z","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading output.log","runtime_seconds":0.446115041},{"desc":"uploading wandb-summary.json","runtime_seconds":0.446107595},{"desc":"uploading config.yaml","runtime_seconds":0.171185441}],"total_operations":3}}
{"time":"2025-05-29T07:14:25.066454743Z","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-29T07:14:25.700290212Z","level":"INFO","msg":"stream: closing","id":"qyqhjhx0"}
{"time":"2025-05-29T07:14:25.70030729Z","level":"INFO","msg":"handler: closed","stream_id":"qyqhjhx0"}
{"time":"2025-05-29T07:14:25.700322101Z","level":"INFO","msg":"sender: closed","stream_id":"qyqhjhx0"}
{"time":"2025-05-29T07:14:25.700315419Z","level":"INFO","msg":"writer: Close: closed","stream_id":"qyqhjhx0"}
{"time":"2025-05-29T07:14:25.700392382Z","level":"INFO","msg":"stream: closed","id":"qyqhjhx0"}
