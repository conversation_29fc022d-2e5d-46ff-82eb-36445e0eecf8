📊 Loading Amazon data...
Train: 1087075, Val: 195702, Test: 233089
Training P5 on Amazon...
Epoch 1:   0%|                                                                                                                                                          | 0/33972 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "train_p5_amazon_wandb.py", line 281, in <module>
    main()
  File "train_p5_amazon_wandb.py", line 265, in main
    results = train_p5_amazon()
  File "train_p5_amazon_wandb.py", line 177, in train_p5_amazon
    outputs = model(prompts)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "train_p5_amazon_wandb.py", line 110, in forward
    embedded = self.embedding(tokens)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/sparse.py", line 164, in forward
    return F.embedding(
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/functional.py", line 2267, in embedding
    return torch.embedding(weight, input, padding_idx, scale_grad_by_freq, sparse)
RuntimeError: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu! (when checking argument for argument index in method wrapper_CUDA__index_select)
Traceback (most recent call last):
  File "train_p5_amazon_wandb.py", line 281, in <module>
    main()
  File "train_p5_amazon_wandb.py", line 265, in main
    results = train_p5_amazon()
  File "train_p5_amazon_wandb.py", line 177, in train_p5_amazon
    outputs = model(prompts)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "train_p5_amazon_wandb.py", line 110, in forward
    embedded = self.embedding(tokens)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/sparse.py", line 164, in forward
    return F.embedding(
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/nn/functional.py", line 2267, in embedding
    return torch.embedding(weight, input, padding_idx, scale_grad_by_freq, sparse)
RuntimeError: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu! (when checking argument for argument index in method wrapper_CUDA__index_select)
