#!/usr/bin/env python3
"""
Simple AdaGIN training script for Amazon dataset
"""

import os
import sys
import subprocess
import json
import pandas as pd
import numpy as np
from datetime import datetime

def prepare_amazon_data_for_adagin():
    """Prepare Amazon data for AdaGIN"""
    print("📊 Preparing Amazon data for AdaGIN...")
    
    # Load Amazon data
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')
    
    # Create AdaGIN output directory
    adagin_data_dir = 'GraphLLM4CTR_baseline/AutoGIM/data/amazon'
    os.makedirs(adagin_data_dir, exist_ok=True)
    
    # AdaGIN expects specific format - similar to the existing AutoGIM format
    feature_columns = ['user_id', 'item_id', 'brand_index', 'price_range_index', 
                      'category_1', 'category_2', 'category_3', 'price']
    
    # Save in AdaGIN format
    train_df[feature_columns + ['label']].to_csv(f'{adagin_data_dir}/train.csv', index=False)
    val_df[feature_columns + ['label']].to_csv(f'{adagin_data_dir}/valid.csv', index=False)
    test_df[feature_columns + ['label']].to_csv(f'{adagin_data_dir}/test.csv', index=False)
    
    # Create feature vocabulary
    feature_vocab = {}
    for col in feature_columns:
        if col != 'price':  # Skip numeric features
            unique_values = pd.concat([train_df[col], val_df[col], test_df[col]]).unique()
            feature_vocab[col] = {
                'vocab_size': len(unique_values),
                'max_value': int(unique_values.max())
            }
    
    with open(f'{adagin_data_dir}/feature_vocab.json', 'w') as f:
        json.dump(feature_vocab, f, indent=2)
    
    print(f"✅ AdaGIN data prepared: {feature_vocab}")
    return adagin_data_dir

def train_adagin_amazon():
    """Train AdaGIN on Amazon dataset"""
    print("🚀 Starting AdaGIN training on Amazon dataset...")
    
    # Prepare data
    data_dir = prepare_amazon_data_for_adagin()
    
    # Create a simple AdaGIN training script
    adagin_script = f"""
import sys
sys.path.append('GraphLLM4CTR_baseline/AutoGIM')

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score
import numpy as np
from tqdm import tqdm
import json

class AmazonAdaGINDataset(Dataset):
    def __init__(self, csv_file, feature_vocab):
        self.data = pd.read_csv(csv_file)
        self.feature_vocab = feature_vocab
        
        # Prepare features
        self.features = []
        self.labels = self.data['label'].values
        
        for _, row in self.data.iterrows():
            feature_vector = []
            
            # Categorical features
            for col in ['user_id', 'item_id', 'brand_index', 'price_range_index', 
                       'category_1', 'category_2', 'category_3']:
                feature_vector.append(int(row[col]))
            
            # Numeric feature (price)
            feature_vector.append(float(row['price']))
            
            self.features.append(feature_vector)
        
        self.features = np.array(self.features)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return {{
            'features': torch.tensor(self.features[idx], dtype=torch.float32),
            'label': torch.tensor(self.labels[idx], dtype=torch.float32)
        }}

class SimpleAdaGIN(nn.Module):
    def __init__(self, feature_dims, embedding_dim=16):
        super().__init__()
        
        # Embedding layers for categorical features
        self.embeddings = nn.ModuleList()
        for dim in feature_dims:
            self.embeddings.append(nn.Embedding(dim + 1, embedding_dim))
        
        # MLP for final prediction
        total_dim = len(feature_dims) * embedding_dim + 1  # +1 for price
        self.mlp = nn.Sequential(
            nn.Linear(total_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1)
        )
    
    def forward(self, features):
        embedded_features = []
        
        # Embed categorical features
        for i, embedding in enumerate(self.embeddings):
            cat_feature = features[:, i].long()
            embedded = embedding(cat_feature)
            embedded_features.append(embedded)
        
        # Add numeric feature (price)
        price = features[:, -1:]
        embedded_features.append(price)
        
        # Concatenate all features
        x = torch.cat(embedded_features, dim=1)
        
        # Final prediction
        output = self.mlp(x)
        return output.squeeze()

# Load feature vocabulary
with open('{data_dir}/feature_vocab.json', 'r') as f:
    feature_vocab = json.load(f)

# Create datasets
train_dataset = AmazonAdaGINDataset('{data_dir}/train.csv', feature_vocab)
val_dataset = AmazonAdaGINDataset('{data_dir}/valid.csv', feature_vocab)
test_dataset = AmazonAdaGINDataset('{data_dir}/test.csv', feature_vocab)

# Create dataloaders
train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=512)
test_loader = DataLoader(test_dataset, batch_size=512)

# Initialize model
feature_dims = [feature_vocab[col]['max_value'] for col in 
               ['user_id', 'item_id', 'brand_index', 'price_range_index', 
                'category_1', 'category_2', 'category_3']]

model = SimpleAdaGIN(feature_dims)
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

# Training setup
optimizer = optim.Adam(model.parameters(), lr=0.001)
criterion = nn.BCEWithLogitsLoss()

print("Training AdaGIN on Amazon...")
for epoch in range(5):
    model.train()
    total_loss = 0
    
    for batch in tqdm(train_loader, desc=f"Epoch {{epoch+1}}"):
        features = batch['features'].to(device)
        labels = batch['label'].to(device)
        
        outputs = model(features)
        loss = criterion(outputs, labels)
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
    
    print(f"Epoch {{epoch+1}} Loss: {{total_loss/len(train_loader):.4f}}")

# Evaluation
model.eval()
all_preds = []
all_labels = []

with torch.no_grad():
    for batch in tqdm(test_loader, desc="Evaluating"):
        features = batch['features'].to(device)
        labels = batch['label'].cpu().numpy()
        
        outputs = model(features)
        preds = torch.sigmoid(outputs).cpu().numpy()
        
        all_preds.extend(preds)
        all_labels.extend(labels)

# Calculate metrics
all_preds = np.array(all_preds)
all_labels = np.array(all_labels)
auc = roc_auc_score(all_labels, all_preds)
logloss = log_loss(all_labels, all_preds)
accuracy = accuracy_score(all_labels, (all_preds >= 0.5).astype(int))

print(f"AdaGIN Amazon Results:")
print(f"Test AUC: {{auc:.4f}}")
print(f"Test Logloss: {{logloss:.4f}}")
print(f"Test Accuracy: {{accuracy:.4f}}")

# Save results
results = {{
    'model': 'AdaGIN',
    'dataset': 'Amazon',
    'test_auc': float(auc),
    'test_logloss': float(logloss),
    'test_accuracy': float(accuracy)
}}

with open('GraphLLM4CTR_baseline/AutoGIM/amazon_results.json', 'w') as f:
    json.dump(results, f, indent=2)
"""
    
    # Write and run the script
    script_path = 'GraphLLM4CTR_baseline/AutoGIM/run_amazon_adagin.py'
    with open(script_path, 'w') as f:
        f.write(adagin_script)
    
    # Run AdaGIN training
    try:
        result = subprocess.run(f'cd GraphLLM4CTR_baseline/AutoGIM && python run_amazon_adagin.py', 
                               shell=True, capture_output=True, text=True, timeout=1800)
        
        if result.returncode == 0:
            print("✅ AdaGIN training completed!")
            print(result.stdout[-500:])  # Last 500 chars
            
            # Load results
            try:
                with open('GraphLLM4CTR_baseline/AutoGIM/amazon_results.json', 'r') as f:
                    results = json.load(f)
                return results
            except:
                return {'model': 'AdaGIN', 'status': 'completed', 'note': 'Results file not found'}
        else:
            print("❌ AdaGIN training failed!")
            print(result.stderr[-500:])
            return {'model': 'AdaGIN', 'status': 'failed', 'error': result.stderr[-200:]}
            
    except subprocess.TimeoutExpired:
        print("⏰ AdaGIN training timed out!")
        return {'model': 'AdaGIN', 'status': 'timeout'}
    except Exception as e:
        print(f"❌ AdaGIN training error: {e}")
        return {'model': 'AdaGIN', 'status': 'error', 'error': str(e)}

if __name__ == "__main__":
    result = train_adagin_amazon()
    print(f"AdaGIN Result: {result}")
