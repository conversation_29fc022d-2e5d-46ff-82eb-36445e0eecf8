#!/usr/bin/env python3
"""
Master script to train all baseline models on Amazon dataset for comparative study
"""

import os
import sys
import json
import time
import traceback
from datetime import datetime
import pandas as pd

def run_model_training(model_name, script_path):
    """Run training for a specific model"""
    print(f"\n{'='*60}")
    print(f"🚀 Starting {model_name} training...")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # Import and run the training script
        if model_name == "DCNv2":
            from train_amazon_dcnv2 import train_amazon_dcnv2
            results = train_amazon_dcnv2()
        elif model_name == "GraphPro":
            from train_amazon_graphpro import train_amazon_graphpro
            results = train_amazon_graphpro()
        elif model_name == "AdaGIN":
            from train_amazon_adagin import train_amazon_adagin
            results = train_amazon_adagin()
        elif model_name == "P5":
            from train_amazon_p5 import train_amazon_p5
            results = train_amazon_p5()
        elif model_name == "UniSRec":
            from train_amazon_unisrec import train_amazon_unisrec
            results = train_amazon_unisrec()
        else:
            print(f"❌ Unknown model: {model_name}")
            return None
        
        end_time = time.time()
        training_time = end_time - start_time
        
        if results:
            results['training_time_seconds'] = training_time
            results['training_time_minutes'] = training_time / 60
            
            print(f"✅ {model_name} training completed successfully!")
            print(f"⏱️  Training time: {training_time/60:.2f} minutes")
            print(f"📊 Results: AUC={results.get('test_auc', 'N/A'):.4f}, "
                  f"Logloss={results.get('test_logloss', 'N/A'):.4f}")
        else:
            print(f"❌ {model_name} training failed!")
            results = {
                'model': model_name,
                'status': 'failed',
                'training_time_seconds': training_time
            }
        
        return results
        
    except Exception as e:
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"❌ {model_name} training failed with exception: {e}")
        traceback.print_exc()
        
        return {
            'model': model_name,
            'status': 'failed',
            'error': str(e),
            'training_time_seconds': training_time
        }

def main():
    """Run all baseline model training"""
    print("🎯 Amazon Dataset Comparative Study")
    print("Training all baseline models...")
    print(f"Start time: {datetime.now().isoformat()}")
    
    # List of models to train
    models_to_train = [
        ("DCNv2", "train_amazon_dcnv2.py"),
        ("GraphPro", "train_amazon_graphpro.py"),
        ("AdaGIN", "train_amazon_adagin.py"),
        ("P5", "train_amazon_p5.py"),
        ("UniSRec", "train_amazon_unisrec.py")
    ]
    
    # Store all results
    all_results = []
    successful_models = []
    failed_models = []
    
    total_start_time = time.time()
    
    # Train each model
    for model_name, script_path in models_to_train:
        try:
            results = run_model_training(model_name, script_path)
            all_results.append(results)
            
            if results and results.get('status') != 'failed':
                successful_models.append(model_name)
            else:
                failed_models.append(model_name)
                
        except Exception as e:
            print(f"❌ Failed to run {model_name}: {e}")
            failed_models.append(model_name)
            all_results.append({
                'model': model_name,
                'status': 'failed',
                'error': str(e)
            })
    
    total_end_time = time.time()
    total_time = total_end_time - total_start_time
    
    # Create summary
    summary = {
        'experiment': 'Amazon Dataset Comparative Study',
        'timestamp': datetime.now().isoformat(),
        'total_time_hours': total_time / 3600,
        'models_trained': len(models_to_train),
        'successful_models': successful_models,
        'failed_models': failed_models,
        'success_rate': len(successful_models) / len(models_to_train),
        'detailed_results': all_results
    }
    
    # Save summary
    output_dir = 'amazon_comparative_study_results'
    os.makedirs(output_dir, exist_ok=True)
    
    with open(f'{output_dir}/summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    # Create results table
    results_table = []
    for result in all_results:
        if result and result.get('status') != 'failed':
            results_table.append({
                'Model': result.get('model', 'Unknown'),
                'AUC': f"{result.get('test_auc', 0):.4f}",
                'Logloss': f"{result.get('test_logloss', 0):.4f}",
                'Accuracy': f"{result.get('test_accuracy', 0):.4f}",
                'Training Time (min)': f"{result.get('training_time_minutes', 0):.2f}"
            })
        else:
            results_table.append({
                'Model': result.get('model', 'Unknown'),
                'AUC': 'Failed',
                'Logloss': 'Failed',
                'Accuracy': 'Failed',
                'Training Time (min)': f"{result.get('training_time_seconds', 0)/60:.2f}"
            })
    
    results_df = pd.DataFrame(results_table)
    results_df.to_csv(f'{output_dir}/results_table.csv', index=False)
    
    # Print final summary
    print(f"\n{'='*80}")
    print("🎉 AMAZON COMPARATIVE STUDY COMPLETED!")
    print(f"{'='*80}")
    print(f"📊 Total time: {total_time/3600:.2f} hours")
    print(f"✅ Successful models: {len(successful_models)}/{len(models_to_train)}")
    print(f"❌ Failed models: {len(failed_models)}")
    
    if successful_models:
        print(f"\n✅ Successful models: {', '.join(successful_models)}")
    
    if failed_models:
        print(f"\n❌ Failed models: {', '.join(failed_models)}")
    
    print(f"\n📁 Results saved to: {output_dir}/")
    print(f"📄 Summary: {output_dir}/summary.json")
    print(f"📊 Results table: {output_dir}/results_table.csv")
    
    # Print results table
    print(f"\n📊 RESULTS TABLE:")
    print(results_df.to_string(index=False))

if __name__ == "__main__":
    main()
