from argparse import ArgumentTypeError
import os
import numpy as np
import torch
import random
def get_column(dataset_name):
    if dataset_name == "Criteo":
        numeric_features = ["I1", "I2", "I3", "I4", "I5", "I6", "I7", "I8", "I9", "I10", "I11", "I12", "I13"]
        categorical_features = ["C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "C10", "C11", "C12", "C13", "C14", "C15", "C16","C17", "C18", "C19", "C20", "C21", "C22", "C23", "C24", "C25", "C26"]
        label_col = 'Label'
        return numeric_features, categorical_features, label_col
    elif dataset_name == "Avazu":
        numeric_features = []
        categorical_features = ["C1", "banner_pos", "site_id", "site_domain", "site_category", "app_id", "app_domain",
                                "app_category", "device_id", "device_ip", "device_model", "device_type", "device_conn_type",
                                "C14", "C15", "C16", "C17", "C18", "C19", "C20", "C21","hour"]
        label_col = 'click'
        return numeric_features, categorical_features, label_col
    elif dataset_name == "AntM2C":
        numeric_features = []
        categorical_features = ['user_id', 'item_id', 'deep_features_14','deep_features_19', 'deep_features_20', 'deep_features_21','deep_features_22', 'deep_features_23', 'deep_features_24','deep_features_25', 'deep_features_26','hour','weekday','weekend']
        label_col = 'label'
        return numeric_features, categorical_features, label_col
    elif dataset_name == "Synthetic":
        numeric_features = ["user_age", "item_price"]
        categorical_features = ['user_gender', 'user_region', 'device_type','ad_category', 'ad_placement','hour_of_day', 'day_of_week', 'scene_type']
        label_col = "click"
        return numeric_features, categorical_features, label_col
    elif dataset_name == "MovieLens":
        # Based on the processed MovieLens dataset
        numeric_features = ["age", "occupation", "zip_code_index"]
        categorical_features = ["user_id", "movie_id", "gender", "genres_index"]  # Include genres_index for multi-hot encoding
        label_col = "label"  # Binary label (0 or 1)
        return numeric_features, categorical_features, label_col
    elif dataset_name == "BookCrossing":
        # Based on the processed BookCrossing dataset
        numeric_features = ["age", "year"]
        categorical_features = ["user_index", "item_index", "city_index", "state_index", "country_index", "author_index", "publisher_index"]
        label_col = "label"  # Binary label (0 or 1)
        return numeric_features, categorical_features, label_col
    elif dataset_name == "Amazon":
        # Based on the processed Amazon dataset (original structure)
        numeric_features = ["price"]  # Keep price as numeric for now
        categorical_features = ["user_id", "item_id", "brand_index", "price_range_index", "category_indices"]
        label_col = "label"  # Binary label (0 or 1)
        return numeric_features, categorical_features, label_col
    elif dataset_name == "AmazonCTR":
        # Based on the processed Amazon dataset with new category columns
        numeric_features = ["price"]  # Keep price as numeric for now
        categorical_features = ["user_id", "item_id", "brand_index", "price_range_index", "category_1", "category_2", "category_3"]
        label_col = "label"  # Binary label (0 or 1)
        return numeric_features, categorical_features, label_col
    else:
        raise ValueError(f"Dataset {dataset_name} columns need to be supplied")

class DictObject:
    def __init__(self, dictionary):
        self.__dict__.update(dictionary)

    def items(self):
        return self.__dict__.items()

    def __getitem__(self, key):
        if key in self.__dict__:
            return self.__dict__[key]
        raise KeyError(f"Key {key} not found. Available keys: {list(self.__dict__.keys())}")

    def __contains__(self, key):
        return key in self.__dict__

    def __str__(self):
        return str(self.__dict__)

    def get(self, key, default=None):
        """
        Add get method to support dictionary-like access with defaults
        """
        return self.__dict__.get(key, default)

def dict_to_object(dictionary):
    if isinstance(dictionary, dict):
        converted_dict = {}
        for key, value in dictionary.items():
            if isinstance(value, dict):
                converted_dict[key] = dict_to_object(value)
            else:
                converted_dict[key] = value
        return DictObject(converted_dict)
    return dictionary

def print_to_list(data):
    return ' - '.join('{}: {:.6f}'.format(k, v) for k, v in data.items())

def parse_metric_dict(value):
    try:
        metric_dict = eval(value)
        if not isinstance(metric_dict, dict):
            raise ArgumentTypeError("Must be a dictionary")
        return metric_dict
    except:
        raise ArgumentTypeError("Invalid dictionary format")

def seed_everything(seed=1029):
    random.seed(seed)
    os.environ["PYTHONHASHSEED"] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.deterministic = True