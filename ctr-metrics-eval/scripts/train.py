import os
import io
import sys
import logging
import argparse
import gc
import json
from datetime import datetime
import wandb
wandb.login(anonymous="allow")

# Add project root directory to Python path
from pathlib import Path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))
from src.data_process.data_processor import DataProcessor
from src.data_process.data_loader import data_generator
from src.data_process.utils import get_column, dict_to_object, print_to_list, parse_metric_dict, seed_everything
from src.models.base_model import get_model
def parse_args():
    parser = argparse.ArgumentParser(description='CTR Prediction Benchmark')
    parser.add_argument('--dataset_name', type=str, default='Criteo', help='Dataset name used to train model, e.g., Criteo, Avazu, AntM2C, Synthetic')
    parser.add_argument('--dataset_path', type=str, default='../data/public_data/Criteo_x4', help='Path to data directory')
    parser.add_argument('--model_name', type=str, default='FM', help='Model name: LR, FM, DeepFM, etc.')
    parser.add_argument('--model_output_path', type=str, default='../output/', help='Folder to save model output')
    parser.add_argument('--metrics', nargs='+', default=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'],help='Evaluation metrics use to check model performance')
    parser.add_argument('--verbose', type=int, default=1, help='Verbose')
    parser.add_argument('--optimizer', type=str, default='adam', help=' Optimizer')
    parser.add_argument('--loss', type=str, default='binary_crossentropy', help='Loss function')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=10000, help='Batch size')
    parser.add_argument('--embedding_dim', type=int, default=16, help='Embedding dimension')
    parser.add_argument('--embedding_dropout', type=float, default=0, help='Dropout rate')
    parser.add_argument('--every_x_epochs', type=int, default=1, help='Every x epochs')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--monitor', type=parse_metric_dict, default={'AUC-ROC': 1, 'Logloss': -1},help='TO DO')
    parser.add_argument('--monitor_mode', type=str, default='max',help='Monitor mode')
    parser.add_argument('--patience', type=int, default=2, help='Patience for early stop')
    parser.add_argument('--regularizer', type=float, default=1.0e-05, help='Regularization')
    parser.add_argument('--save_best_only', action='store_false', help='Save best model only')
    parser.add_argument('--seed', type=int, default=2019, help='Random seed')
    parser.add_argument('--task', type=str, default='binary_classification',help='Task type')
    parser.add_argument('--log_dir', type=str, default='../output/logs/', help='Directory to save logs')
    return parser.parse_args()

def main():
    # Parse arguments
    args = parse_args()

    # Initialize wandb
    timestamp = datetime.now().strftime('%Y%m%d_%H%M')
    wandb.init(
        project=f"ctr-prediction-{args.dataset_name}",
        name=f"{args.model_name}_{timestamp}_seed{args.seed}",
        config=vars(args)
    )

    #Set seed
    seed_everything(seed=args.seed)

    # Set up logging
    # Create log directory if it doesn't exist
    os.makedirs(args.log_dir, exist_ok=True)
    log_filename = f"{args.dataset_name}_{args.model_name}_{args.seed}_{timestamp}.log"
    print(f'=== log file name is {log_filename}===')
    log_path = Path(args.log_dir) / log_filename
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[logging.StreamHandler(sys.stdout),
                  logging.FileHandler(log_path)]
    )
    logger = logging.getLogger(__name__)

    # Log all arguments for reference
    logger.info(f"all args: {args}")

    # Data processing
    # if dataset_path or dataset_name doesn't exist, then raise error
    if not os.path.exists(args.dataset_path):
        raise ValueError(f"Dataset path does not exist: {args.dataset_path}")
    if args.dataset_name not in ['Criteo', 'Avazu', 'AntM2C', 'Synthetic', 'MovieLens', 'BookCrossing', 'Amazon', 'AmazonCTR']:
        raise ValueError(f"Dataset name does not exist, please check dataset name.")

    logger.info(f"Start process {args.dataset_name} !")
    processor = DataProcessor(dataset_name=args.dataset_name,
                              data_dir=args.dataset_path,
                              model_name=args.model_name,
                              model_output_path=args.model_output_path)
    # If processed data file exitst, skip processing
    # Use different feature files for AmazonCTR
    if args.dataset_name == "AmazonCTR":
        pickle_file = os.path.join(args.dataset_path, "feature_encoder_amazonctr.pkl")
        json_file = os.path.join(args.dataset_path, "feature_map_amazonctr.json")
    else:
        pickle_file = os.path.join(args.dataset_path, "feature_encoder.pkl")
        json_file = os.path.join(args.dataset_path, "feature_map.json")
    if os.path.exists(pickle_file):
        print('Pickle feature_encode exists, loading...')
        processor.load_pickle(pickle_file)
    else:
        # Get feature encoder and feature map
        processor.process_encoder_map()

    # Load processed data
    logger.info(f"Loading {args.dataset_name} dataset")
    numeric_features, categorical_features, label_col = get_column(args.dataset_name)

    # Use different file names for AmazonCTR dataset
    if args.dataset_name == "AmazonCTR":
        train_file = "train_new.csv"
        val_file = "val_new.csv"
        test_file = "test_new.csv"
    else:
        train_file = "train.csv"
        val_file = "val.csv"
        test_file = "test.csv"

    train_gen, valid_gen, test_gen = data_generator(train_data = os.path.join(args.dataset_path, train_file),
                                                    valid_data = os.path.join(args.dataset_path, val_file),
                                                    test_data = os.path.join(args.dataset_path, test_file),
                                                    DataProcessor = processor,
                                                    batch_size = args.batch_size,
                                                    output_path = os.path.join(args.model_output_path, args.model_name, args.dataset_name),
                                                    numeric_features = numeric_features,
                                                    categorical_features = categorical_features,
                                                    label_col = label_col,
                                                    shuffle_train = False,
                                                    shuffle_valid = False,
                                                    shuffle_test = False,
                                                    )

    # Load feature map
    with io.open(json_file, "r", encoding="utf-8") as fd:
        feature_map = json.load(fd)
        feature_map = dict_to_object(feature_map)

    # Train model
    model_class = get_model(args.model_name)
    args_dict = vars(args)
    model = model_class(feature_map,
                        model_id=args.model_name,
                        **args_dict)
    logger.info(f"Model: {model}")
    # print number of parameters used in model
    logger.info(f"Model parameters: {model.count_parameters()}")

    logger.info(f"Start training model")
    model.fit_generator(train_gen,
                        validation_data=valid_gen,
                        stage='train',
                        **args_dict)
    model.load_weights(model.checkpoint)

    # Eval model
    logger.info(f"Start evaluate model")
    valid_result = model.evaluate_generator(valid_gen, stage='valid')
    gc.collect()

    # Test model
    logger.info(f"Start testing model")
    test_result = model.evaluate_generator(test_gen, stage='test')

    # Mark the result
    result_filename = os.path.join(args.model_output_path, args.model_name, args.dataset_name, f'result_seed{args.seed}.csv')
    timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
    command = ' '.join(sys.argv)
    result_line = (f"{timestamp},"
                   f"[command] python {command},"
                   f"[dataset] {args.dataset_name},"
                   f"[model] {args.model_name},"
                   f"[train] {print_to_list(valid_result)},"
                   f"[val] {print_to_list(valid_result)},"
                   f"[test] {print_to_list(test_result)}"
                   )
    with open(result_filename, 'a+') as f:
        f.write(result_line)

    wandb.finish()


if __name__ == "__main__":
    main()