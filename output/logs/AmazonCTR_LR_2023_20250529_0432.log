2025-05-29 04:32:51 INFO all args: Namespace(batch_size=256, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=16, embedding_dropout=0, epochs=1, every_x_epochs=1, learning_rate=0.001, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='LR', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=2, regularizer=0.00012, save_best_only=True, seed=2023, task='binary_classification', verbose=1)
2025-05-29 04:32:51 INFO Start process AmazonCTR !
2025-05-29 04:32:51 INFO Loading AmazonCTR dataset
2025-05-29 04:32:51 INFO Load h5 data from /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:32:51 INFO Load h5 data from /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:32:51 INFO Load h5 data from /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:32:51 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:32:51 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:32:51 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:32:51 INFO Loading data done
2025-05-29 04:32:51 INFO Model: LR(
  (lr_layer): LR_Layer(
    (final_activation): Sigmoid()
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (price): Embedding(8100, 1, padding_idx=8099)
        (user_id): Embedding(185029, 1, padding_idx=185028)
        (item_id): Embedding(55560, 1, padding_idx=55559)
        (brand_index): Embedding(11108, 1, padding_idx=11107)
        (price_range_index): Embedding(7, 1, padding_idx=6)
        (category_indices): Embedding(45, 1, padding_idx=44)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
)
2025-05-29 04:32:51 INFO Model parameters: 259850
2025-05-29 04:32:51 INFO Start training model
2025-05-29 04:32:51 INFO Start training: 4247 batches/epoch
2025-05-29 04:32:51 INFO ************ Epoch=1 start ************
