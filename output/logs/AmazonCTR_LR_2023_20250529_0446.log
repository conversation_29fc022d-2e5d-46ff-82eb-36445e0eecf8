2025-05-29 04:46:48 INFO all args: Namespace(batch_size=256, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=16, embedding_dropout=0, epochs=1, every_x_epochs=1, learning_rate=0.001, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='LR', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=2, regularizer=0.00012, save_best_only=True, seed=2023, task='binary_classification', verbose=1)
2025-05-29 04:46:48 INFO Start process AmazonCTR !
2025-05-29 04:46:48 INFO Start get feature encoder and feature map
2025-05-29 04:46:50 INFO Reading training data from /data/datasets/processed_datasets/amazon/train_new.csv
2025-05-29 04:46:51 INFO Fill NaN done!
2025-05-29 04:46:51 INFO Process categorical column: price 
2025-05-29 04:46:52 INFO Process categorical column: user_id 
2025-05-29 04:46:52 INFO Process categorical column: item_id 
2025-05-29 04:46:53 INFO Process categorical column: brand_index 
2025-05-29 04:46:53 INFO Process categorical column: price_range_index 
2025-05-29 04:46:53 INFO Process categorical column: category_1 
2025-05-29 04:46:54 INFO Process categorical column: category_2 
2025-05-29 04:46:54 INFO Process categorical column: category_3 
2025-05-29 04:46:54 INFO Set feature index
2025-05-29 04:46:54 INFO Pickle feature_encode: /data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl
2025-05-29 04:46:54 INFO Save feature_map to json: /data/datasets/processed_datasets/amazon/feature_map_amazonctr.json
2025-05-29 04:46:54 INFO Loading AmazonCTR dataset
2025-05-29 04:46:57 INFO Start process data for training, validation or testing
2025-05-29 04:46:57 INFO Fill NaN done!
2025-05-29 04:46:57 INFO Transform feature
2025-05-29 04:46:59 INFO Saving h5 data at /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:46:59 INFO Start process data for training, validation or testing
2025-05-29 04:46:59 INFO Fill NaN done!
2025-05-29 04:46:59 INFO Transform feature
2025-05-29 04:47:00 INFO Saving h5 data at /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:47:00 INFO Start process data for training, validation or testing
2025-05-29 04:47:00 INFO Fill NaN done!
2025-05-29 04:47:00 INFO Transform feature
2025-05-29 04:47:01 INFO Saving h5 data at /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:47:01 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:47:01 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:47:01 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:47:01 INFO Loading data done
2025-05-29 04:47:01 INFO Model: LR(
  (lr_layer): LR_Layer(
    (final_activation): Sigmoid()
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (price): Embedding(8100, 1, padding_idx=8099)
        (user_id): Embedding(185029, 1, padding_idx=185028)
        (item_id): Embedding(55560, 1, padding_idx=55559)
        (brand_index): Embedding(11108, 1, padding_idx=11107)
        (price_range_index): Embedding(7, 1, padding_idx=6)
        (category_1): Embedding(2, 1, padding_idx=1)
        (category_2): Embedding(4, 1, padding_idx=3)
        (category_3): Embedding(41, 1, padding_idx=40)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
)
2025-05-29 04:47:01 INFO Model parameters: 259852
2025-05-29 04:47:01 INFO Start training model
2025-05-29 04:47:01 INFO Start training: 4247 batches/epoch
2025-05-29 04:47:01 INFO ************ Epoch=1 start ************
2025-05-29 04:47:37 INFO [Metrics] AUC-ROC: 0.601121 - AUC-PR: 0.766627 - ACC: 0.694924 - Precision: 0.694919 - Recall: 0.999904 - F1: 0.819970 - MCC: 0.013726 - Logloss: 0.604882 - MSE: 0.207672 - RMSE: 0.455711 - COPC: 1.020848 - KLD: 0.359295
2025-05-29 04:47:37 INFO Save best model: monitor(max): -0.003761
2025-05-29 04:47:37 INFO --- 4247/4247 batches finished ---
2025-05-29 04:47:37 INFO Train loss: 0.618268
2025-05-29 04:47:37 INFO ************ Epoch=1 end ************
2025-05-29 04:47:37 INFO Training finished.
2025-05-29 04:47:37 INFO Load best model: /root/code/ctr-metrics-eval/output_amazon/LR/AmazonCTR/LR_model_seed2023.ckpt
2025-05-29 04:47:37 INFO Start evaluate model
2025-05-29 04:47:41 INFO [Metrics] AUC-ROC: 0.601121 - AUC-PR: 0.766627 - ACC: 0.694924 - Precision: 0.694919 - Recall: 0.999904 - F1: 0.819970 - MCC: 0.013726 - Logloss: 0.604882 - MSE: 0.207672 - RMSE: 0.455711 - COPC: 1.020848 - KLD: 0.359295
2025-05-29 04:47:41 INFO Start testing model
2025-05-29 04:47:45 INFO [Metrics] AUC-ROC: 0.595682 - AUC-PR: 0.760531 - ACC: 0.692774 - Precision: 0.692787 - Recall: 0.999901 - F1: 0.818483 - MCC: 0.009072 - Logloss: 0.607412 - MSE: 0.208823 - RMSE: 0.456971 - COPC: 1.016910 - KLD: 0.362723
