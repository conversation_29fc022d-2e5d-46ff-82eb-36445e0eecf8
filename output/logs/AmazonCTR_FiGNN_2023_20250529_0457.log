2025-05-29 04:57:56 INFO all args: Namespace(batch_size=1024, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=16, embedding_dropout=0, epochs=2, every_x_epochs=1, learning_rate=0.01, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='FiGNN', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=2, regularizer=0.1, save_best_only=True, seed=2023, task='binary_classification', verbose=1)
2025-05-29 04:57:56 INFO Start process AmazonCTR !
2025-05-29 04:57:56 INFO Load feature_processor from pickle: /data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl
2025-05-29 04:57:56 INFO Loading AmazonCTR dataset
2025-05-29 04:57:56 INFO Load h5 data from /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:57:56 INFO Load h5 data from /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:57:56 INFO Load h5 data from /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:57:56 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:57:56 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:57:56 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:57:56 INFO Loading data done
2025-05-29 04:57:57 INFO Model: FiGNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (price): Embedding(8100, 16, padding_idx=8099)
      (user_id): Embedding(185029, 16, padding_idx=185028)
      (item_id): Embedding(55560, 16, padding_idx=55559)
      (brand_index): Embedding(11108, 16, padding_idx=11107)
      (price_range_index): Embedding(7, 16, padding_idx=6)
      (category_1): Embedding(2, 16, padding_idx=1)
      (category_2): Embedding(4, 16, padding_idx=3)
      (category_3): Embedding(41, 16, padding_idx=40)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fignn): FiGNN_Layer(
    (gnn): ModuleList(
      (0): GraphLayer()
    )
    (leaky_relu): LeakyReLU(negative_slope=0.01)
    (W_attn): Linear(in_features=32, out_features=1, bias=False)
  )
  (fc): PredictionLayer(
    (mlp1): Linear(in_features=16, out_features=1, bias=False)
    (mlp2): Sequential(
      (0): Linear(in_features=128, out_features=8, bias=False)
      (1): Sigmoid()
    )
  )
  (final_activation): Sigmoid()
)
2025-05-29 04:57:57 INFO Model parameters: 4162800
2025-05-29 04:57:57 INFO Start training model
2025-05-29 04:57:57 INFO Start training: 1062 batches/epoch
2025-05-29 04:57:57 INFO ************ Epoch=1 start ************
2025-05-29 04:58:13 INFO [Metrics] AUC-ROC: 0.543755 - AUC-PR: 0.736361 - ACC: 0.569662 - Precision: 0.713141 - Recall: 0.636802 - F1: 0.672813 - MCC: 0.050864 - Logloss: 0.812688 - MSE: 0.284534 - RMSE: 0.533417 - COPC: 1.353595 - KLD: 0.525331
2025-05-29 04:58:13 INFO Save best model: monitor(max): -0.268933
2025-05-29 04:58:13 INFO --- 1062/1062 batches finished ---
2025-05-29 04:58:13 INFO Train loss: 0.621635
2025-05-29 04:58:13 INFO ************ Epoch=1 end ************
2025-05-29 04:58:28 INFO [Metrics] AUC-ROC: 0.490250 - AUC-PR: 0.710202 - ACC: 0.475815 - Precision: 0.665826 - Recall: 0.493036 - F1: 0.566549 - MCC: -0.064823 - Logloss: 1.397154 - MSE: 0.411768 - RMSE: 0.641691 - COPC: 1.245827 - KLD: 0.906465
2025-05-29 04:58:28 INFO Monitor(max) STOP: -0.906904 !
2025-05-29 04:58:28 INFO Reduce learning rate on plateau: 0.001000
2025-05-29 04:58:28 INFO --- 1062/1062 batches finished ---
2025-05-29 04:58:28 INFO Train loss: 0.520680
2025-05-29 04:58:28 INFO ************ Epoch=2 end ************
2025-05-29 04:58:28 INFO Training finished.
2025-05-29 04:58:28 INFO Load best model: /root/code/ctr-metrics-eval/output_amazon/FiGNN/AmazonCTR/FiGNN_model_seed2023.ckpt
2025-05-29 04:58:28 INFO Start evaluate model
2025-05-29 04:58:30 INFO [Metrics] AUC-ROC: 0.543755 - AUC-PR: 0.736361 - ACC: 0.569662 - Precision: 0.713141 - Recall: 0.636802 - F1: 0.672813 - MCC: 0.050864 - Logloss: 0.812688 - MSE: 0.284534 - RMSE: 0.533417 - COPC: 1.353595 - KLD: 0.525331
2025-05-29 04:58:30 INFO Start testing model
2025-05-29 04:58:33 INFO [Metrics] AUC-ROC: 0.536376 - AUC-PR: 0.729245 - ACC: 0.561936 - Precision: 0.708152 - Recall: 0.625350 - F1: 0.664180 - MCC: 0.041955 - Logloss: 0.822458 - MSE: 0.288601 - RMSE: 0.537216 - COPC: 1.361405 - KLD: 0.534510
