2025-05-29 04:51:47 INFO all args: Namespace(batch_size=512, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=10, embedding_dropout=0, epochs=4, every_x_epochs=1, learning_rate=0.005, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='DeepFM', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=2, regularizer=0.01, save_best_only=True, seed=2023, task='binary_classification', verbose=1)
2025-05-29 04:51:47 INFO Start process AmazonCTR !
2025-05-29 04:51:47 INFO Load feature_processor from pickle: /data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl
2025-05-29 04:51:47 INFO Loading AmazonCTR dataset
2025-05-29 04:51:47 INFO Load h5 data from /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:51:47 INFO Load h5 data from /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:51:47 INFO Load h5 data from /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:51:47 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:51:47 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:51:47 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:51:47 INFO Loading data done
2025-05-29 04:51:48 INFO Model: DeepFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (price): Embedding(8100, 10)
        (user_id): Embedding(185029, 10)
        (item_id): Embedding(55560, 10)
        (brand_index): Embedding(11108, 10)
        (price_range_index): Embedding(7, 10)
        (category_1): Embedding(2, 10)
        (category_2): Embedding(4, 10)
        (category_3): Embedding(41, 10)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (fm_layer): FM_Layer_v2(
    (inner_product_layer): InnerProductLayer_v2()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (price): Embedding(8100, 1, padding_idx=8099)
          (user_id): Embedding(185029, 1, padding_idx=185028)
          (item_id): Embedding(55560, 1, padding_idx=55559)
          (brand_index): Embedding(11108, 1, padding_idx=11107)
          (price_range_index): Embedding(7, 1, padding_idx=6)
          (category_1): Embedding(2, 1, padding_idx=1)
          (category_2): Embedding(4, 1, padding_idx=3)
          (category_3): Embedding(41, 1, padding_idx=40)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=80, out_features=400, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.5, inplace=False)
      (3): Linear(in_features=400, out_features=400, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.5, inplace=False)
      (6): Linear(in_features=400, out_features=400, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.5, inplace=False)
      (9): Linear(in_features=400, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2025-05-29 04:51:48 INFO Model parameters: 3211962
2025-05-29 04:51:48 INFO Start training model
2025-05-29 04:51:48 INFO Start training: 2124 batches/epoch
2025-05-29 04:51:48 INFO ************ Epoch=1 start ************
2025-05-29 04:52:08 INFO [Metrics] AUC-ROC: 0.650321 - AUC-PR: 0.796800 - ACC: 0.700432 - Precision: 0.709993 - Recall: 0.961663 - F1: 0.816883 - MCC: 0.131708 - Logloss: 0.585878 - MSE: 0.199600 - RMSE: 0.446766 - COPC: 1.005479 - KLD: 0.350773
2025-05-29 04:52:08 INFO Save best model: monitor(max): 0.064443
2025-05-29 04:52:08 INFO --- 2124/2124 batches finished ---
2025-05-29 04:52:08 INFO Train loss: 0.608079
2025-05-29 04:52:08 INFO ************ Epoch=1 end ************
2025-05-29 04:52:27 INFO [Metrics] AUC-ROC: 0.655065 - AUC-PR: 0.801352 - ACC: 0.687458 - Precision: 0.738966 - Recall: 0.850682 - F1: 0.790898 - MCC: 0.191648 - Logloss: 0.635292 - MSE: 0.209704 - RMSE: 0.457935 - COPC: 0.983233 - KLD: 0.365444
2025-05-29 04:52:27 INFO Monitor(max) STOP: 0.019773 !
2025-05-29 04:52:27 INFO Reduce learning rate on plateau: 0.000500
2025-05-29 04:52:27 INFO --- 2124/2124 batches finished ---
2025-05-29 04:52:27 INFO Train loss: 0.453083
2025-05-29 04:52:27 INFO ************ Epoch=2 end ************
2025-05-29 04:52:46 INFO [Metrics] AUC-ROC: 0.662796 - AUC-PR: 0.808316 - ACC: 0.680923 - Precision: 0.748141 - Recall: 0.815220 - F1: 0.780241 - MCC: 0.204435 - Logloss: 0.684732 - MSE: 0.217682 - RMSE: 0.466564 - COPC: 0.990551 - KLD: 0.384930
2025-05-29 04:52:46 INFO Monitor(max) STOP: -0.021936 !
2025-05-29 04:52:46 INFO Reduce learning rate on plateau: 0.000050
2025-05-29 04:52:46 INFO Early stopping at epoch=3
2025-05-29 04:52:46 INFO --- 2124/2124 batches finished ---
2025-05-29 04:52:46 INFO Train loss: 0.384599
2025-05-29 04:52:46 INFO Training finished.
2025-05-29 04:52:46 INFO Load best model: /root/code/ctr-metrics-eval/output_amazon/DeepFM/AmazonCTR/DeepFM_model_seed2023.ckpt
2025-05-29 04:52:46 INFO Start evaluate model
2025-05-29 04:52:49 INFO [Metrics] AUC-ROC: 0.650321 - AUC-PR: 0.796800 - ACC: 0.700432 - Precision: 0.709993 - Recall: 0.961663 - F1: 0.816883 - MCC: 0.131708 - Logloss: 0.585878 - MSE: 0.199600 - RMSE: 0.446766 - COPC: 1.005479 - KLD: 0.350773
2025-05-29 04:52:49 INFO Start testing model
2025-05-29 04:52:52 INFO [Metrics] AUC-ROC: 0.640641 - AUC-PR: 0.788450 - ACC: 0.696867 - Precision: 0.705884 - Recall: 0.964123 - F1: 0.815037 - MCC: 0.119511 - Logloss: 0.591593 - MSE: 0.201937 - RMSE: 0.449374 - COPC: 0.998458 - KLD: 0.355426
