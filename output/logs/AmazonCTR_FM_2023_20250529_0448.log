2025-05-29 04:48:59 INFO all args: Namespace(batch_size=256, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=32, embedding_dropout=0, epochs=3, every_x_epochs=1, learning_rate=0.001, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='FM', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=2, regularizer=0.0005, save_best_only=True, seed=2023, task='binary_classification', verbose=1)
2025-05-29 04:48:59 INFO Start process AmazonCTR !
2025-05-29 04:48:59 INFO Load feature_processor from pickle: /data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl
2025-05-29 04:48:59 INFO Loading AmazonCTR dataset
2025-05-29 04:48:59 INFO Load h5 data from /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:48:59 INFO Load h5 data from /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:48:59 INFO Load h5 data from /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:48:59 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:48:59 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:48:59 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:48:59 INFO Loading data done
2025-05-29 04:48:59 INFO Model: FM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (price): Embedding(8100, 32, padding_idx=8099)
      (user_id): Embedding(185029, 32, padding_idx=185028)
      (item_id): Embedding(55560, 32, padding_idx=55559)
      (brand_index): Embedding(11108, 32, padding_idx=11107)
      (price_range_index): Embedding(7, 32, padding_idx=6)
      (category_1): Embedding(2, 32, padding_idx=1)
      (category_2): Embedding(4, 32, padding_idx=3)
      (category_3): Embedding(41, 32, padding_idx=40)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fm_layer): FM_Layer(
    (inner_product_layer): InnerProductLayer()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (price): Embedding(8100, 1, padding_idx=8099)
          (user_id): Embedding(185029, 1, padding_idx=185028)
          (item_id): Embedding(55560, 1, padding_idx=55559)
          (brand_index): Embedding(11108, 1, padding_idx=11107)
          (price_range_index): Embedding(7, 1, padding_idx=6)
          (category_1): Embedding(2, 1, padding_idx=1)
          (category_2): Embedding(4, 1, padding_idx=3)
          (category_3): Embedding(41, 1, padding_idx=40)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
    (final_activation): Sigmoid()
  )
)
2025-05-29 04:48:59 INFO Model parameters: 8575084
2025-05-29 04:48:59 INFO Start training model
2025-05-29 04:49:00 INFO Start training: 4247 batches/epoch
2025-05-29 04:49:00 INFO ************ Epoch=1 start ************
2025-05-29 04:49:44 INFO [Metrics] AUC-ROC: 0.578914 - AUC-PR: 0.754209 - ACC: 0.694720 - Precision: 0.695070 - Recall: 0.998823 - F1: 0.819712 - MCC: 0.013741 - Logloss: 0.607923 - MSE: 0.209011 - RMSE: 0.457178 - COPC: 1.031283 - KLD: 0.360486
2025-05-29 04:49:44 INFO Save best model: monitor(max): -0.029009
2025-05-29 04:49:44 INFO --- 4247/4247 batches finished ---
2025-05-29 04:49:44 INFO Train loss: 0.640647
2025-05-29 04:49:44 INFO ************ Epoch=1 end ************
2025-05-29 04:50:28 INFO [Metrics] AUC-ROC: 0.580017 - AUC-PR: 0.754808 - ACC: 0.694663 - Precision: 0.695076 - Recall: 0.998654 - F1: 0.819659 - MCC: 0.013359 - Logloss: 0.607673 - MSE: 0.208906 - RMSE: 0.457062 - COPC: 1.031044 - KLD: 0.360380
2025-05-29 04:50:28 INFO Save best model: monitor(max): -0.027656
2025-05-29 04:50:28 INFO --- 4247/4247 batches finished ---
2025-05-29 04:50:28 INFO Train loss: 0.643295
2025-05-29 04:50:28 INFO ************ Epoch=2 end ************
2025-05-29 04:51:13 INFO [Metrics] AUC-ROC: 0.581128 - AUC-PR: 0.755511 - ACC: 0.694658 - Precision: 0.695101 - Recall: 0.998551 - F1: 0.819642 - MCC: 0.014081 - Logloss: 0.607441 - MSE: 0.208807 - RMSE: 0.456954 - COPC: 1.030876 - KLD: 0.360281
2025-05-29 04:51:13 INFO Save best model: monitor(max): -0.026313
2025-05-29 04:51:13 INFO --- 4247/4247 batches finished ---
2025-05-29 04:51:13 INFO Train loss: 0.642772
2025-05-29 04:51:13 INFO ************ Epoch=3 end ************
2025-05-29 04:51:13 INFO Training finished.
2025-05-29 04:51:13 INFO Load best model: /root/code/ctr-metrics-eval/output_amazon/FM/AmazonCTR/FM_model_seed2023.ckpt
2025-05-29 04:51:13 INFO Start evaluate model
2025-05-29 04:51:17 INFO [Metrics] AUC-ROC: 0.581128 - AUC-PR: 0.755511 - ACC: 0.694658 - Precision: 0.695101 - Recall: 0.998551 - F1: 0.819642 - MCC: 0.014081 - Logloss: 0.607441 - MSE: 0.208807 - RMSE: 0.456954 - COPC: 1.030876 - KLD: 0.360281
2025-05-29 04:51:17 INFO Start testing model
2025-05-29 04:51:23 INFO [Metrics] AUC-ROC: 0.575351 - AUC-PR: 0.749031 - ACC: 0.692624 - Precision: 0.692991 - Recall: 0.998749 - F1: 0.818239 - MCC: 0.013974 - Logloss: 0.609938 - MSE: 0.209934 - RMSE: 0.458186 - COPC: 1.027380 - KLD: 0.363701
