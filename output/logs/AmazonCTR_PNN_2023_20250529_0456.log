2025-05-29 04:56:20 INFO all args: Namespace(batch_size=512, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=10, embedding_dropout=0, epochs=3, every_x_epochs=1, learning_rate=0.001, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='PNN', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=2, regularizer=0.01, save_best_only=True, seed=2023, task='binary_classification', verbose=1)
2025-05-29 04:56:20 INFO Start process AmazonCTR !
2025-05-29 04:56:20 INFO Load feature_processor from pickle: /data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl
2025-05-29 04:56:20 INFO Loading AmazonCTR dataset
2025-05-29 04:56:20 INFO Load h5 data from /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:56:20 INFO Load h5 data from /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:56:20 INFO Load h5 data from /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:56:20 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:56:20 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:56:20 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:56:20 INFO Loading data done
2025-05-29 04:56:21 INFO Model: PNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (price): Embedding(8100, 10, padding_idx=8099)
      (user_id): Embedding(185029, 10, padding_idx=185028)
      (item_id): Embedding(55560, 10, padding_idx=55559)
      (brand_index): Embedding(11108, 10, padding_idx=11107)
      (price_range_index): Embedding(7, 10, padding_idx=6)
      (category_1): Embedding(2, 10, padding_idx=1)
      (category_2): Embedding(4, 10, padding_idx=3)
      (category_3): Embedding(41, 10, padding_idx=40)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=108, out_features=400, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.5, inplace=False)
      (3): Linear(in_features=400, out_features=400, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.5, inplace=False)
      (6): Linear(in_features=400, out_features=400, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.5, inplace=False)
      (9): Linear(in_features=400, out_features=1, bias=True)
      (10): Sigmoid()
    )
  )
)
2025-05-29 04:56:21 INFO Model parameters: 2963311
2025-05-29 04:56:21 INFO Start training model
2025-05-29 04:56:21 INFO Start training: 2124 batches/epoch
2025-05-29 04:56:21 INFO ************ Epoch=1 start ************
2025-05-29 04:56:42 INFO [Metrics] AUC-ROC: 0.704258 - AUC-PR: 0.829906 - ACC: 0.710652 - Precision: 0.719180 - Recall: 0.957405 - F1: 0.821368 - MCC: 0.185741 - Logloss: 0.567855 - MSE: 0.190782 - RMSE: 0.436786 - COPC: 0.949539 - KLD: 0.339132
2025-05-29 04:56:42 INFO Save best model: monitor(max): 0.136403
2025-05-29 04:56:42 INFO --- 2124/2124 batches finished ---
2025-05-29 04:56:42 INFO Train loss: 0.608291
2025-05-29 04:56:42 INFO ************ Epoch=1 end ************
2025-05-29 04:57:02 INFO [Metrics] AUC-ROC: 0.715127 - AUC-PR: 0.836616 - ACC: 0.715578 - Precision: 0.767874 - Recall: 0.846571 - F1: 0.805304 - MCC: 0.287054 - Logloss: 0.831064 - MSE: 0.206709 - RMSE: 0.454652 - COPC: 1.018875 - KLD: 0.380269
2025-05-29 04:57:02 INFO Monitor(max) STOP: -0.115937 !
2025-05-29 04:57:02 INFO Reduce learning rate on plateau: 0.000100
2025-05-29 04:57:02 INFO --- 2124/2124 batches finished ---
2025-05-29 04:57:02 INFO Train loss: 0.464688
2025-05-29 04:57:02 INFO ************ Epoch=2 end ************
2025-05-29 04:57:23 INFO [Metrics] AUC-ROC: 0.721537 - AUC-PR: 0.842299 - ACC: 0.712921 - Precision: 0.778505 - Recall: 0.820184 - F1: 0.798801 - MCC: 0.300355 - Logloss: 0.647121 - MSE: 0.195390 - RMSE: 0.442029 - COPC: 0.998870 - KLD: 0.350871
2025-05-29 04:57:23 INFO Monitor(max) STOP: 0.074415 !
2025-05-29 04:57:23 INFO Reduce learning rate on plateau: 0.000010
2025-05-29 04:57:23 INFO Early stopping at epoch=3
2025-05-29 04:57:23 INFO --- 2124/2124 batches finished ---
2025-05-29 04:57:23 INFO Train loss: 0.410925
2025-05-29 04:57:23 INFO Training finished.
2025-05-29 04:57:23 INFO Load best model: /root/code/ctr-metrics-eval/output_amazon/PNN/AmazonCTR/PNN_model_seed2023.ckpt
2025-05-29 04:57:23 INFO Start evaluate model
2025-05-29 04:57:25 INFO [Metrics] AUC-ROC: 0.704258 - AUC-PR: 0.829906 - ACC: 0.710652 - Precision: 0.719180 - Recall: 0.957405 - F1: 0.821368 - MCC: 0.185741 - Logloss: 0.567855 - MSE: 0.190782 - RMSE: 0.436786 - COPC: 0.949539 - KLD: 0.339132
2025-05-29 04:57:26 INFO Start testing model
2025-05-29 04:57:29 INFO [Metrics] AUC-ROC: 0.688091 - AUC-PR: 0.817773 - ACC: 0.705070 - Precision: 0.713666 - Recall: 0.959026 - F1: 0.818351 - MCC: 0.166542 - Logloss: 0.581469 - MSE: 0.195694 - RMSE: 0.442373 - COPC: 0.944547 - KLD: 0.346041
