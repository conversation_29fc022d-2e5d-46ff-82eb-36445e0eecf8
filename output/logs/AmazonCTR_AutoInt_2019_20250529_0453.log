2025-05-29 04:53:34 INFO all args: Namespace(batch_size=512, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=16, embedding_dropout=0, epochs=4, every_x_epochs=1, learning_rate=0.001, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='AutoInt', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=2, regularizer=0.01, save_best_only=True, seed=2019, task='binary_classification', verbose=1)
2025-05-29 04:53:34 INFO Start process AmazonCTR !
2025-05-29 04:53:34 INFO Load feature_processor from pickle: /data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl
2025-05-29 04:53:34 INFO Loading AmazonCTR dataset
2025-05-29 04:53:34 INFO Load h5 data from /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:53:34 INFO Load h5 data from /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:53:34 INFO Load h5 data from /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:53:34 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:53:34 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:53:34 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:53:34 INFO Loading data done
2025-05-29 04:53:35 INFO Model: AutoInt(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (price): Embedding(8100, 16, padding_idx=8099)
      (user_id): Embedding(185029, 16, padding_idx=185028)
      (item_id): Embedding(55560, 16, padding_idx=55559)
      (brand_index): Embedding(11108, 16, padding_idx=11107)
      (price_range_index): Embedding(7, 16, padding_idx=6)
      (category_1): Embedding(2, 16, padding_idx=1)
      (category_2): Embedding(4, 16, padding_idx=3)
      (category_3): Embedding(41, 16, padding_idx=40)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=128, out_features=400, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.1, inplace=False)
      (3): Linear(in_features=400, out_features=400, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.1, inplace=False)
      (6): Linear(in_features=400, out_features=400, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.1, inplace=False)
      (9): Linear(in_features=400, out_features=1, bias=True)
    )
  )
  (self_attention): Sequential(
    (0): MultiHeadSelfAttention(
      (W_q): Linear(in_features=16, out_features=64, bias=False)
      (W_k): Linear(in_features=16, out_features=64, bias=False)
      (W_v): Linear(in_features=16, out_features=64, bias=False)
      (W_res): Linear(in_features=16, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (dropout): Dropout(p=0.1, inplace=False)
        (softmax): Softmax(dim=2)
      )
      (dropout): Dropout(p=0.1, inplace=False)
    )
    (1): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (dropout): Dropout(p=0.1, inplace=False)
        (softmax): Softmax(dim=2)
      )
      (dropout): Dropout(p=0.1, inplace=False)
    )
    (2): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (dropout): Dropout(p=0.1, inplace=False)
        (softmax): Softmax(dim=2)
      )
      (dropout): Dropout(p=0.1, inplace=False)
    )
  )
  (fc): Linear(in_features=512, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2025-05-29 04:53:35 INFO Model parameters: 4559602
2025-05-29 04:53:35 INFO Start training model
2025-05-29 04:53:35 INFO Start training: 2124 batches/epoch
2025-05-29 04:53:35 INFO ************ Epoch=1 start ************
2025-05-29 04:54:00 INFO [Metrics] AUC-ROC: 0.706169 - AUC-PR: 0.831840 - ACC: 0.705992 - Precision: 0.708355 - Recall: 0.980585 - F1: 0.822531 - MCC: 0.147575 - Logloss: 0.565806 - MSE: 0.190970 - RMSE: 0.437001 - COPC: 0.932730 - KLD: 0.338709
2025-05-29 04:54:00 INFO Save best model: monitor(max): 0.140363
2025-05-29 04:54:00 INFO --- 2124/2124 batches finished ---
2025-05-29 04:54:00 INFO Train loss: 0.607050
2025-05-29 04:54:00 INFO ************ Epoch=1 end ************
2025-05-29 04:54:24 INFO [Metrics] AUC-ROC: 0.708414 - AUC-PR: 0.832049 - ACC: 0.712072 - Precision: 0.767891 - Recall: 0.839305 - F1: 0.802011 - MCC: 0.281939 - Logloss: 1.054650 - MSE: 0.224064 - RMSE: 0.473354 - COPC: 1.037651 - KLD: 0.473307
2025-05-29 04:54:24 INFO Monitor(max) STOP: -0.346236 !
2025-05-29 04:54:24 INFO Reduce learning rate on plateau: 0.000100
2025-05-29 04:54:24 INFO --- 2124/2124 batches finished ---
2025-05-29 04:54:24 INFO Train loss: 0.452052
2025-05-29 04:54:24 INFO ************ Epoch=2 end ************
2025-05-29 04:54:48 INFO [Metrics] AUC-ROC: 0.717852 - AUC-PR: 0.839889 - ACC: 0.712185 - Precision: 0.774189 - Recall: 0.826979 - F1: 0.799714 - MCC: 0.292448 - Logloss: 0.657074 - MSE: 0.199694 - RMSE: 0.446871 - COPC: 1.008920 - KLD: 0.362610
2025-05-29 04:54:48 INFO Monitor(max) STOP: 0.060778 !
2025-05-29 04:54:48 INFO Reduce learning rate on plateau: 0.000010
2025-05-29 04:54:48 INFO Early stopping at epoch=3
2025-05-29 04:54:48 INFO --- 2124/2124 batches finished ---
2025-05-29 04:54:48 INFO Train loss: 0.401187
2025-05-29 04:54:48 INFO Training finished.
2025-05-29 04:54:48 INFO Load best model: /root/code/ctr-metrics-eval/output_amazon/AutoInt/AmazonCTR/AutoInt_model_seed2019.ckpt
2025-05-29 04:54:48 INFO Start evaluate model
2025-05-29 04:54:51 INFO [Metrics] AUC-ROC: 0.706169 - AUC-PR: 0.831840 - ACC: 0.705992 - Precision: 0.708355 - Recall: 0.980585 - F1: 0.822531 - MCC: 0.147575 - Logloss: 0.565806 - MSE: 0.190970 - RMSE: 0.437001 - COPC: 0.932730 - KLD: 0.338709
2025-05-29 04:54:51 INFO Start testing model
2025-05-29 04:54:54 INFO [Metrics] AUC-ROC: 0.691183 - AUC-PR: 0.820584 - ACC: 0.701844 - Precision: 0.704343 - Recall: 0.981656 - F1: 0.820193 - MCC: 0.133088 - Logloss: 0.577442 - MSE: 0.195268 - RMSE: 0.441891 - COPC: 0.927448 - KLD: 0.344817
