2025-05-29 04:55:07 INFO all args: Namespace(batch_size=512, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=10, embedding_dropout=0, epochs=4, every_x_epochs=1, learning_rate=0.001, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='DeepFM', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=2, regularizer=0.01, save_best_only=True, seed=2023, task='binary_classification', verbose=1)
2025-05-29 04:55:07 INFO Start process AmazonCTR !
2025-05-29 04:55:07 INFO Load feature_processor from pickle: /data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl
2025-05-29 04:55:07 INFO Loading AmazonCTR dataset
2025-05-29 04:55:07 INFO Load h5 data from /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:55:07 INFO Load h5 data from /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:55:07 INFO Load h5 data from /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:55:07 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:55:07 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:55:07 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:55:07 INFO Loading data done
2025-05-29 04:55:08 INFO Model: DeepFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (price): Embedding(8100, 10)
        (user_id): Embedding(185029, 10)
        (item_id): Embedding(55560, 10)
        (brand_index): Embedding(11108, 10)
        (price_range_index): Embedding(7, 10)
        (category_1): Embedding(2, 10)
        (category_2): Embedding(4, 10)
        (category_3): Embedding(41, 10)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (fm_layer): FM_Layer_v2(
    (inner_product_layer): InnerProductLayer_v2()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (price): Embedding(8100, 1, padding_idx=8099)
          (user_id): Embedding(185029, 1, padding_idx=185028)
          (item_id): Embedding(55560, 1, padding_idx=55559)
          (brand_index): Embedding(11108, 1, padding_idx=11107)
          (price_range_index): Embedding(7, 1, padding_idx=6)
          (category_1): Embedding(2, 1, padding_idx=1)
          (category_2): Embedding(4, 1, padding_idx=3)
          (category_3): Embedding(41, 1, padding_idx=40)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=80, out_features=400, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.5, inplace=False)
      (3): Linear(in_features=400, out_features=400, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.5, inplace=False)
      (6): Linear(in_features=400, out_features=400, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.5, inplace=False)
      (9): Linear(in_features=400, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2025-05-29 04:55:08 INFO Model parameters: 3211962
2025-05-29 04:55:08 INFO Start training model
2025-05-29 04:55:08 INFO Start training: 2124 batches/epoch
2025-05-29 04:55:08 INFO ************ Epoch=1 start ************
2025-05-29 04:55:27 INFO [Metrics] AUC-ROC: 0.706858 - AUC-PR: 0.832687 - ACC: 0.706728 - Precision: 0.709808 - Recall: 0.977585 - F1: 0.822449 - MCC: 0.153430 - Logloss: 0.560274 - MSE: 0.189349 - RMSE: 0.435142 - COPC: 0.968516 - KLD: 0.339123
2025-05-29 04:55:27 INFO Save best model: monitor(max): 0.146584
2025-05-29 04:55:27 INFO --- 2124/2124 batches finished ---
2025-05-29 04:55:27 INFO Train loss: 0.607175
2025-05-29 04:55:27 INFO ************ Epoch=1 end ************
2025-05-29 04:55:47 INFO [Metrics] AUC-ROC: 0.718357 - AUC-PR: 0.841717 - ACC: 0.714081 - Precision: 0.773338 - Recall: 0.832502 - F1: 0.801830 - MCC: 0.293747 - Logloss: 0.607066 - MSE: 0.193195 - RMSE: 0.439539 - COPC: 0.998368 - KLD: 0.342964
2025-05-29 04:55:47 INFO Monitor(max) STOP: 0.111291 !
2025-05-29 04:55:47 INFO Reduce learning rate on plateau: 0.000100
2025-05-29 04:55:47 INFO --- 2124/2124 batches finished ---
2025-05-29 04:55:47 INFO Train loss: 0.464787
2025-05-29 04:55:47 INFO ************ Epoch=2 end ************
2025-05-29 04:56:06 INFO [Metrics] AUC-ROC: 0.723415 - AUC-PR: 0.843638 - ACC: 0.713861 - Precision: 0.780344 - Recall: 0.818610 - F1: 0.799019 - MCC: 0.304530 - Logloss: 0.678538 - MSE: 0.197750 - RMSE: 0.444691 - COPC: 0.990013 - KLD: 0.356199
2025-05-29 04:56:06 INFO Monitor(max) STOP: 0.044877 !
2025-05-29 04:56:06 INFO Reduce learning rate on plateau: 0.000010
2025-05-29 04:56:06 INFO Early stopping at epoch=3
2025-05-29 04:56:06 INFO --- 2124/2124 batches finished ---
2025-05-29 04:56:06 INFO Train loss: 0.408250
2025-05-29 04:56:06 INFO Training finished.
2025-05-29 04:56:06 INFO Load best model: /root/code/ctr-metrics-eval/output_amazon/DeepFM/AmazonCTR/DeepFM_model_seed2023.ckpt
2025-05-29 04:56:06 INFO Start evaluate model
2025-05-29 04:56:09 INFO [Metrics] AUC-ROC: 0.706858 - AUC-PR: 0.832687 - ACC: 0.706728 - Precision: 0.709808 - Recall: 0.977585 - F1: 0.822449 - MCC: 0.153430 - Logloss: 0.560274 - MSE: 0.189349 - RMSE: 0.435142 - COPC: 0.968516 - KLD: 0.339123
2025-05-29 04:56:09 INFO Start testing model
2025-05-29 04:56:12 INFO [Metrics] AUC-ROC: 0.691949 - AUC-PR: 0.821599 - ACC: 0.702766 - Precision: 0.705737 - Recall: 0.979216 - F1: 0.820283 - MCC: 0.140257 - Logloss: 0.569865 - MSE: 0.193161 - RMSE: 0.439500 - COPC: 0.963143 - KLD: 0.345048
