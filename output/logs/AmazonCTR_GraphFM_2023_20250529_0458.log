2025-05-29 04:58:53 INFO all args: Namespace(batch_size=1024, dataset_name='AmazonCTR', dataset_path='/data/datasets/processed_datasets/amazon', embedding_dim=16, embedding_dropout=0, epochs=10, every_x_epochs=1, learning_rate=0.01, log_dir='../output/logs/', loss='binary_crossentropy', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], model_name='GraphFM', model_output_path='output_amazon/', monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', optimizer='adam', patience=3, regularizer=0.1, save_best_only=True, seed=2023, task='binary_classification', verbose=1)
2025-05-29 04:58:53 INFO Start process AmazonCTR !
2025-05-29 04:58:53 INFO Load feature_processor from pickle: /data/datasets/processed_datasets/amazon/feature_encoder_amazonctr.pkl
2025-05-29 04:58:53 INFO Loading AmazonCTR dataset
2025-05-29 04:58:53 INFO Load h5 data from /data/datasets/processed_datasets/amazon/train_new.h5
2025-05-29 04:58:53 INFO Load h5 data from /data/datasets/processed_datasets/amazon/val_new.h5
2025-05-29 04:58:53 INFO Load h5 data from /data/datasets/processed_datasets/amazon/test_new.h5
2025-05-29 04:58:53 INFO Train samples: total/1087075, pos/746798, neg/340277, ratio/68.70%
2025-05-29 04:58:53 INFO Validation samples: total/195702, pos/135978, neg/59724, ratio/69.48%
2025-05-29 04:58:53 INFO Test samples: total/233089, pos/161468, neg/71621, ratio/69.27%
2025-05-29 04:58:53 INFO Loading data done
2025-05-29 04:58:53 INFO Model: GraphFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (price): Embedding(8100, 16, padding_idx=8099)
      (user_id): Embedding(185029, 16, padding_idx=185028)
      (item_id): Embedding(55560, 16, padding_idx=55559)
      (brand_index): Embedding(11108, 16, padding_idx=11107)
      (price_range_index): Embedding(7, 16, padding_idx=6)
      (category_1): Embedding(2, 16, padding_idx=1)
      (category_2): Embedding(4, 16, padding_idx=3)
      (category_3): Embedding(41, 16, padding_idx=40)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (mlp1): Linear(in_features=16, out_features=1, bias=False)
  (mlp2): Sequential(
    (0): Linear(in_features=128, out_features=8, bias=False)
    (1): Sigmoid()
  )
  (final_activation): Sigmoid()
)
2025-05-29 04:58:53 INFO Model parameters: 4158656
2025-05-29 04:58:53 INFO Start training model
2025-05-29 04:58:54 INFO Start training: 1062 batches/epoch
2025-05-29 04:58:54 INFO ************ Epoch=1 start ************
2025-05-29 04:59:03 INFO [Metrics] AUC-ROC: 0.651970 - AUC-PR: 0.799080 - ACC: 0.704929 - Precision: 0.708541 - Recall: 0.977371 - F1: 0.821523 - MCC: 0.143085 - Logloss: 0.587605 - MSE: 0.200194 - RMSE: 0.447431 - COPC: 1.046134 - KLD: 0.349989
2025-05-29 04:59:03 INFO Save best model: monitor(max): 0.064365
2025-05-29 04:59:03 INFO --- 1062/1062 batches finished ---
2025-05-29 04:59:03 INFO Train loss: 0.607024
2025-05-29 04:59:03 INFO ************ Epoch=1 end ************
2025-05-29 04:59:11 INFO [Metrics] AUC-ROC: 0.695093 - AUC-PR: 0.825212 - ACC: 0.689221 - Precision: 0.770042 - Recall: 0.788061 - F1: 0.778947 - MCC: 0.256266 - Logloss: 0.686095 - MSE: 0.215577 - RMSE: 0.464303 - COPC: 1.017337 - KLD: 0.397053
2025-05-29 04:59:11 INFO Monitor(max) STOP: 0.008998 !
2025-05-29 04:59:11 INFO Reduce learning rate on plateau: 0.001000
2025-05-29 04:59:11 INFO --- 1062/1062 batches finished ---
2025-05-29 04:59:11 INFO Train loss: 0.417297
2025-05-29 04:59:11 INFO ************ Epoch=2 end ************
2025-05-29 04:59:20 INFO [Metrics] AUC-ROC: 0.705040 - AUC-PR: 0.829546 - ACC: 0.712435 - Precision: 0.764705 - Recall: 0.846637 - F1: 0.803588 - MCC: 0.277103 - Logloss: 0.701871 - MSE: 0.207395 - RMSE: 0.455406 - COPC: 0.965879 - KLD: 0.391787
2025-05-29 04:59:20 INFO Monitor(max) STOP: 0.003169 !
2025-05-29 04:59:20 INFO Reduce learning rate on plateau: 0.000100
2025-05-29 04:59:20 INFO --- 1062/1062 batches finished ---
2025-05-29 04:59:20 INFO Train loss: 0.402515
2025-05-29 04:59:20 INFO ************ Epoch=3 end ************
2025-05-29 04:59:29 INFO [Metrics] AUC-ROC: 0.707738 - AUC-PR: 0.831749 - ACC: 0.711418 - Precision: 0.767583 - Recall: 0.838584 - F1: 0.801514 - MCC: 0.280483 - Logloss: 0.724051 - MSE: 0.208804 - RMSE: 0.456950 - COPC: 0.970414 - KLD: 0.398793
2025-05-29 04:59:29 INFO Monitor(max) STOP: -0.016313 !
2025-05-29 04:59:29 INFO Reduce learning rate on plateau: 0.000010
2025-05-29 04:59:29 INFO Early stopping at epoch=4
2025-05-29 04:59:29 INFO --- 1062/1062 batches finished ---
2025-05-29 04:59:29 INFO Train loss: 0.374093
2025-05-29 04:59:29 INFO Training finished.
2025-05-29 04:59:29 INFO Load best model: /root/code/ctr-metrics-eval/output_amazon/GraphFM/AmazonCTR/GraphFM_model_seed2023.ckpt
2025-05-29 04:59:29 INFO Start evaluate model
2025-05-29 04:59:31 INFO [Metrics] AUC-ROC: 0.651970 - AUC-PR: 0.799080 - ACC: 0.704929 - Precision: 0.708541 - Recall: 0.977371 - F1: 0.821523 - MCC: 0.143085 - Logloss: 0.587605 - MSE: 0.200194 - RMSE: 0.447431 - COPC: 1.046134 - KLD: 0.349989
2025-05-29 04:59:31 INFO Start testing model
2025-05-29 04:59:33 INFO [Metrics] AUC-ROC: 0.644179 - AUC-PR: 0.792486 - ACC: 0.700737 - Precision: 0.704438 - Recall: 0.978578 - F1: 0.819181 - MCC: 0.128227 - Logloss: 0.591373 - MSE: 0.201906 - RMSE: 0.449339 - COPC: 1.039178 - KLD: 0.354380
