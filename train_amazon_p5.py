#!/usr/bin/env python3
"""
Train P5 model on Amazon dataset for comparative study
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime

# Add P5 to path
sys.path.append('GraphLLM4CTR_baseline/P5')

def prepare_amazon_data_for_p5():
    """Prepare Amazon data for P5 training"""
    print("📊 Preparing Amazon data for P5...")
    
    # Load the Amazon data with split categories
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')
    
    print(f"Train shape: {train_df.shape}")
    print(f"Val shape: {val_df.shape}")
    print(f"Test shape: {test_df.shape}")
    
    # Create output directory
    output_dir = 'GraphLLM4CTR_baseline/P5/data/amazon'
    os.makedirs(output_dir, exist_ok=True)
    
    # P5 expects specific format for CTR prediction
    # Convert to P5 format with text templates
    
    def create_p5_text_features(df):
        """Create text features for P5"""
        # Create text descriptions for items using available features
        text_features = []
        
        for _, row in df.iterrows():
            # Create item description
            item_desc = f"Item {row['item_id']} with brand {row['brand_index']} "
            item_desc += f"in categories {row['category_1']}, {row['category_2']}, {row['category_3']} "
            item_desc += f"with price range {row['price_range_index']}"
            
            # Create user description
            user_desc = f"User {row['user_id']}"
            
            # Create interaction text
            interaction_text = f"{user_desc} interacts with {item_desc}"
            
            text_features.append({
                'user_id': row['user_id'],
                'item_id': row['item_id'],
                'user_text': user_desc,
                'item_text': item_desc,
                'interaction_text': interaction_text,
                'label': row['label']
            })
        
        return pd.DataFrame(text_features)
    
    # Convert to P5 format
    train_p5 = create_p5_text_features(train_df)
    val_p5 = create_p5_text_features(val_df)
    test_p5 = create_p5_text_features(test_df)
    
    # Save P5 format data
    train_p5.to_csv(f'{output_dir}/train.csv', index=False)
    val_p5.to_csv(f'{output_dir}/val.csv', index=False)
    test_p5.to_csv(f'{output_dir}/test.csv', index=False)
    
    # Create P5 dataset info
    dataset_info = {
        'dataset_name': 'amazon',
        'num_users': max(train_df['user_id'].max(), val_df['user_id'].max(), test_df['user_id'].max()) + 1,
        'num_items': max(train_df['item_id'].max(), val_df['item_id'].max(), test_df['item_id'].max()) + 1,
        'num_interactions': len(train_df) + len(val_df) + len(test_df),
        'task': 'ctr_prediction'
    }
    
    with open(f'{output_dir}/info.json', 'w') as f:
        json.dump(dataset_info, f, indent=2)
    
    print(f"✅ P5 data saved to {output_dir}")
    print(f"📊 Dataset info: {dataset_info}")
    
    return output_dir

def train_amazon_p5():
    """Train P5 on Amazon dataset"""
    print("🚀 Starting P5 training on Amazon dataset...")
    
    # Prepare data
    data_dir = prepare_amazon_data_for_p5()
    if data_dir is None:
        print("❌ Data preparation failed!")
        return
    
    # Import P5 modules
    try:
        from GraphLLM4CTR_baseline.P5.ctr_prediction_p5 import train_p5_ctr
        from GraphLLM4CTR_baseline.P5.src.param import parse_args
    except ImportError as e:
        print(f"❌ Failed to import P5 modules: {e}")
        return None
    
    # Configure P5 arguments
    args_list = [
        '--dataset', 'amazon',
        '--data_path', data_dir,
        '--output_dir', 'GraphLLM4CTR_baseline/P5/output_amazon_p5',
        '--model_name', 'p5',
        '--epochs', '5',  # P5 typically needs fewer epochs
        '--batch_size', '32',  # Smaller batch size for P5
        '--lr', '1e-4',
        '--max_text_length', '512',
        '--seed', '2023',
        '--device', 'cuda' if torch.cuda.is_available() else 'cpu',
        '--task', 'ctr_prediction'
    ]
    
    # Train the model
    try:
        # Parse arguments
        args = parse_args(args_list)
        
        # Run P5 training
        results = train_p5_ctr(args)
        
        # Save results
        output_dir = args.output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        results_dict = {
            'model': 'P5',
            'dataset': 'Amazon',
            'timestamp': datetime.now().isoformat(),
            'test_auc': results.get('test_auc', 0.0),
            'test_logloss': results.get('test_logloss', 0.0),
            'test_accuracy': results.get('test_accuracy', 0.0),
            'task': 'ctr_prediction'
        }
        
        with open(f'{output_dir}/results.json', 'w') as f:
            json.dump(results_dict, f, indent=2)
        
        print("✅ P5 training completed!")
        print(f"📊 Results: AUC={results_dict['test_auc']:.4f}, "
              f"Logloss={results_dict['test_logloss']:.4f}")
        
        return results_dict
        
    except Exception as e:
        print(f"❌ P5 training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    train_amazon_p5()
