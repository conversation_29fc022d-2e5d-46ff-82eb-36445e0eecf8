#!/usr/bin/env python3
"""
Simple UniSRec training script for Amazon dataset
"""

import os
import sys
import subprocess
import json
import pandas as pd
import numpy as np
from datetime import datetime

def prepare_amazon_data_for_unisrec():
    """Prepare Amazon data for UniSRec"""
    print("📊 Preparing Amazon data for UniSRec...")

    # Load Amazon data
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')

    # Create UniSRec output directory
    unisrec_data_dir = 'Unisrec/dataset/amazon'
    os.makedirs(unisrec_data_dir, exist_ok=True)

    # UniSRec expects sequential format, but we'll adapt for CTR prediction
    def create_unisrec_format(df, split_name):
        """Convert to UniSRec format"""
        # For CTR prediction, we'll treat each interaction as a sequence of length 1
        unisrec_data = []

        for _, row in df.iterrows():
            # Create a simple sequence format
            user_id = row['user_id']
            item_id = row['item_id']
            label = row['label']

            # UniSRec format: user_id, item_sequence, target_item, rating
            unisrec_data.append({
                'user_id': user_id,
                'item_sequence': f"[{item_id}]",  # Single item sequence
                'target_item': item_id,
                'rating': label,
                'timestamp': 1  # Dummy timestamp
            })

        # Save UniSRec format
        unisrec_df = pd.DataFrame(unisrec_data)
        unisrec_df.to_csv(f'{unisrec_data_dir}/{split_name}.csv', index=False, sep='\t')
        return unisrec_df

    # Convert all splits
    train_unisrec = create_unisrec_format(train_df, 'train')
    val_unisrec = create_unisrec_format(val_df, 'val')
    test_unisrec = create_unisrec_format(test_df, 'test')

    # Create dataset info
    dataset_info = {
        'dataset': 'amazon',
        'num_users': max(train_df['user_id'].max(), val_df['user_id'].max(), test_df['user_id'].max()) + 1,
        'num_items': max(train_df['item_id'].max(), val_df['item_id'].max(), test_df['item_id'].max()) + 1,
        'train_samples': len(train_unisrec),
        'val_samples': len(val_unisrec),
        'test_samples': len(test_unisrec)
    }

    with open(f'{unisrec_data_dir}/info.json', 'w') as f:
        json.dump(dataset_info, f, indent=2)

    print(f"✅ UniSRec data prepared: {dataset_info}")
    return unisrec_data_dir

def train_unisrec_amazon():
    """Train UniSRec on Amazon dataset"""
    print("🚀 Starting UniSRec training on Amazon dataset...")

    # Prepare data
    data_dir = prepare_amazon_data_for_unisrec()

    # Create a simple UniSRec training script
    unisrec_script = f"""
import sys
sys.path.append('Unisrec')

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score
import numpy as np
from tqdm import tqdm
import json

class AmazonUniSRecDataset(Dataset):
    def __init__(self, csv_file, num_users, num_items):
        self.data = pd.read_csv(csv_file, sep='\t')
        self.num_users = num_users
        self.num_items = num_items

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        user_id = row['user_id']
        target_item = row['target_item']
        rating = row['rating']

        return {{
            'user_id': torch.tensor(user_id, dtype=torch.long),
            'item_id': torch.tensor(target_item, dtype=torch.long),
            'rating': torch.tensor(rating, dtype=torch.float)
        }}

class SimpleUniSRec(nn.Module):
    def __init__(self, num_users, num_items, embedding_dim=64):
        super().__init__()

        self.user_embedding = nn.Embedding(num_users, embedding_dim)
        self.item_embedding = nn.Embedding(num_items, embedding_dim)

        # Simple MLP for prediction
        self.mlp = nn.Sequential(
            nn.Linear(embedding_dim * 2, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 1)
        )

    def forward(self, user_ids, item_ids):
        user_emb = self.user_embedding(user_ids)
        item_emb = self.item_embedding(item_ids)

        # Concatenate embeddings
        combined = torch.cat([user_emb, item_emb], dim=1)

        # Predict rating
        output = self.mlp(combined)
        return output.squeeze()

# Load dataset info
with open('{data_dir}/info.json', 'r') as f:
    dataset_info = json.load(f)

num_users = dataset_info['num_users']
num_items = dataset_info['num_items']

# Create datasets
train_dataset = AmazonUniSRecDataset('{data_dir}/train.csv', num_users, num_items)
val_dataset = AmazonUniSRecDataset('{data_dir}/val.csv', num_users, num_items)
test_dataset = AmazonUniSRecDataset('{data_dir}/test.csv', num_users, num_items)

# Create dataloaders
train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=512)
test_loader = DataLoader(test_dataset, batch_size=512)

# Initialize model
model = SimpleUniSRec(num_users, num_items)
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

# Training setup
optimizer = optim.Adam(model.parameters(), lr=0.001)
criterion = nn.BCEWithLogitsLoss()

print("Training UniSRec on Amazon...")
for epoch in range(5):
    model.train()
    total_loss = 0

    for batch in tqdm(train_loader, desc=f"Epoch {{epoch+1}}"):
        user_ids = batch['user_id'].to(device)
        item_ids = batch['item_id'].to(device)
        ratings = batch['rating'].to(device)

        outputs = model(user_ids, item_ids)
        loss = criterion(outputs, ratings)

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        total_loss += loss.item()

    print(f"Epoch {{epoch+1}} Loss: {{total_loss/len(train_loader):.4f}}")

# Evaluation
model.eval()
all_preds = []
all_labels = []

with torch.no_grad():
    for batch in tqdm(test_loader, desc="Evaluating"):
        user_ids = batch['user_id'].to(device)
        item_ids = batch['item_id'].to(device)
        ratings = batch['rating'].cpu().numpy()

        outputs = model(user_ids, item_ids)
        preds = torch.sigmoid(outputs).cpu().numpy()

        all_preds.extend(preds)
        all_labels.extend(ratings)

# Calculate metrics
all_preds = np.array(all_preds)
all_labels = np.array(all_labels)
auc = roc_auc_score(all_labels, all_preds)
logloss = log_loss(all_labels, all_preds)
accuracy = accuracy_score(all_labels, (all_preds >= 0.5).astype(int))

print(f"UniSRec Amazon Results:")
print(f"Test AUC: {{auc:.4f}}")
print(f"Test Logloss: {{logloss:.4f}}")
print(f"Test Accuracy: {{accuracy:.4f}}")

# Save results
results = {{
    'model': 'UniSRec',
    'dataset': 'Amazon',
    'test_auc': float(auc),
    'test_logloss': float(logloss),
    'test_accuracy': float(accuracy)
}}

with open('Unisrec/amazon_results.json', 'w') as f:
    json.dump(results, f, indent=2)
"""

    # Write and run the script
    script_path = 'Unisrec/run_amazon_unisrec.py'
    with open(script_path, 'w') as f:
        f.write(unisrec_script)

    # Run UniSRec training
    try:
        result = subprocess.run(f'cd Unisrec && python run_amazon_unisrec.py',
                               shell=True, capture_output=True, text=True, timeout=1800)

        if result.returncode == 0:
            print("✅ UniSRec training completed!")
            print(result.stdout[-500:])  # Last 500 chars

            # Load results
            try:
                with open('Unisrec/amazon_results.json', 'r') as f:
                    results = json.load(f)
                return results
            except:
                return {'model': 'UniSRec', 'status': 'completed', 'note': 'Results file not found'}
        else:
            print("❌ UniSRec training failed!")
            print(result.stderr[-500:])
            return {'model': 'UniSRec', 'status': 'failed', 'error': result.stderr[-200:]}

    except subprocess.TimeoutExpired:
        print("⏰ UniSRec training timed out!")
        return {'model': 'UniSRec', 'status': 'timeout'}
    except Exception as e:
        print(f"❌ UniSRec training error: {e}")
        return {'model': 'UniSRec', 'status': 'error', 'error': str(e)}

if __name__ == "__main__":
    result = train_unisrec_amazon()
    print(f"UniSRec Result: {result}")
