import torch


def build_rgcn_input(user_ids, item_ids, loader, labels=None):
    """
    构造用于 R-GCN 的输入图结构：节点特征、边索引、边类型
    使用真实的用户-物品交互数据构建图

    Args:
        user_ids: 用户ID列表
        item_ids: 物品ID列表
        loader: 嵌入加载器
        labels: 交互标签 (1=点击, 0=未点击)

    返回：
        x: Tensor [N, D] - 节点特征
        edge_index: LongTensor [2, E] - 边索引
        edge_type: LongTensor [E] - 边类型
    """
    device = loader.device

    # Get user and item embeddings and ensure they're on the correct device and data type
    user_x = loader.get_user_rgcn_input(user_ids).to(device).float()  # [U, D] or [U, T, D]
    item_x = loader.get_item_rgcn_input(item_ids).to(device).float()  # [I, D] or [I, T, D]

    # Reshape to 2D if needed
    if len(user_x.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        user_x = user_x[:, 0, :]  # Take the first token's embedding

    if len(item_x.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        item_x = item_x[:, 0, :]  # Take the first token's embedding

    x = torch.cat([user_x, item_x], dim=0)         # [U+I, D]

    num_users = len(user_ids)
    edge_index = []
    edge_type = []

    # Build edges based on REAL user-item interactions
    if labels is not None:
        # Use actual interaction data from the batch
        for i, (uid, iid, label) in enumerate(zip(user_ids, item_ids, labels)):
            user_idx = i
            item_idx = num_users + i  # Each item corresponds to its position in the batch

            # Create edge based on actual interaction
            edge_index.append([user_idx, item_idx])

            # Edge type based on actual interaction:
            # 0 = positive interaction (click), 1 = negative interaction (no click)
            edge_type.append(0 if label == 1 else 1)

            # Also add reverse edge (item -> user) for bidirectional information flow
            edge_index.append([item_idx, user_idx])
            edge_type.append(0 if label == 1 else 1)
    else:
        # Fallback: if no labels provided, create minimal connectivity
        # This should only be used during inference
        for i in range(min(len(user_ids), len(item_ids))):
            user_idx = i
            item_idx = num_users + i
            edge_index.append([user_idx, item_idx])
            edge_type.append(0)  # Default to positive edge type

    # Convert to tensors
    if len(edge_index) > 0:
        edge_index = torch.LongTensor(edge_index).t().contiguous().to(device)
        edge_type = torch.LongTensor(edge_type).to(device)
    else:
        # Handle empty edge case
        edge_index = torch.zeros((2, 0), dtype=torch.long, device=device)
        edge_type = torch.zeros(0, dtype=torch.long, device=device)

    return x, edge_index, edge_type


def build_hgt_input(user_ids, item_ids, loader, labels=None):
    """
    构造用于 HGT 的输入图结构：节点特征字典、边索引、边类型、节点类型
    使用真实的用户-物品交互数据构建图

    Args:
        user_ids: 用户ID列表
        item_ids: 物品ID列表
        loader: 嵌入加载器
        labels: 交互标签 (1=点击, 0=未点击)

    返回：
        x_dict: Dict[int, Tensor] - 节点类型到特征的映射
        edge_index: LongTensor [2, E] - 边索引
        edge_type: LongTensor [E] - 边类型
        node_type: LongTensor [N] - 节点类型
    """
    device = loader.device
    x_dict = {}
    node_type = {}
    global_idx = 0

    uid_to_gid = {}
    iid_to_gid = {}

    # Get user features and ensure they're on the correct device and data type
    user_feats = loader.get_user_hgt_input(user_ids).to(device).float()

    # Reshape to 2D: [batch_size, hidden_dim] by taking the first token's embedding
    if len(user_feats.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        user_feats = user_feats[:, 0, :]  # Take the first token's embedding

    x_dict[0] = user_feats
    for i, uid in enumerate(user_ids):
        uid = int(uid) if isinstance(uid, (int, float)) else str(uid)
        node_type[global_idx] = 0
        uid_to_gid[uid] = global_idx
        global_idx += 1

    # Get item features and ensure they're on the correct device and data type
    item_feats = loader.get_item_hgt_input(item_ids).to(device).float()

    # Reshape to 2D: [batch_size, hidden_dim] by taking the first token's embedding
    if len(item_feats.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        item_feats = item_feats[:, 0, :]  # Take the first token's embedding

    x_dict[1] = item_feats
    for i, iid in enumerate(item_ids):
        # Handle both integer and string item IDs (BookCrossing has string IDs)
        iid = int(iid) if isinstance(iid, (int, float)) else str(iid)
        node_type[global_idx] = 1
        iid_to_gid[iid] = global_idx
        global_idx += 1

    edge_index = []
    edge_type = []

    # Build edges based on REAL user-item interactions
    if labels is not None:
        # Use actual interaction data from the batch
        for i, (uid, iid, label) in enumerate(zip(user_ids, item_ids, labels)):
            # Convert IDs to consistent format
            uid = int(uid) if isinstance(uid, (int, float)) else str(uid)
            iid = int(iid) if isinstance(iid, (int, float)) else str(iid)

            u_idx = uid_to_gid[uid]
            i_idx = iid_to_gid[iid]

            # Create edge based on actual interaction
            edge_index.append([u_idx, i_idx])

            # Edge type based on actual interaction:
            # 0 = positive interaction (click), 1 = negative interaction (no click)
            edge_type.append(0 if label == 1 else 1)

            # Also add reverse edge (item -> user) for bidirectional information flow
            edge_index.append([i_idx, u_idx])
            edge_type.append(0 if label == 1 else 1)
    else:
        # Fallback: if no labels provided, create minimal connectivity
        # This should only be used during inference
        for i, (uid, iid) in enumerate(zip(user_ids, item_ids)):
            uid = int(uid) if isinstance(uid, (int, float)) else str(uid)
            iid = int(iid) if isinstance(iid, (int, float)) else str(iid)

            u_idx = uid_to_gid[uid]
            i_idx = iid_to_gid[iid]
            edge_index.append([u_idx, i_idx])
            edge_type.append(0)  # Default to positive edge type

    # Convert to tensors and move to the correct device
    if len(edge_index) > 0:
        edge_index = torch.LongTensor(edge_index).t().contiguous().to(device)
        edge_type = torch.LongTensor(edge_type).to(device)
    else:
        # Handle empty edge case
        edge_index = torch.zeros((2, 0), dtype=torch.long, device=device)
        edge_type = torch.zeros(0, dtype=torch.long, device=device)

    node_type_tensor = torch.tensor([node_type[i] for i in range(len(node_type))], dtype=torch.long).to(device)

    # Validate edge indices
    if len(edge_index) > 0:
        max_node_idx = global_idx - 1
        max_edge_idx = edge_index.max().item()
        if max_edge_idx > max_node_idx:
            print(f"WARNING: Edge index {max_edge_idx} exceeds max node index {max_node_idx}")
            edge_index = torch.clamp(edge_index, 0, max_node_idx)

    return x_dict, edge_index, edge_type, node_type_tensor

