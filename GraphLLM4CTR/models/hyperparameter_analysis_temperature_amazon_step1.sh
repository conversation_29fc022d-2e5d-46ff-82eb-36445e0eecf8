#!/usr/bin/env bash

# Script to run contrastive learning with different temperature values for Amazon dataset
DATA_DIR="/data/datasets/processed_datasets/amazon"
OUTPUT_DIR="/data/datasets/processed_datasets/amazon/temperature_analysis_FAST"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BASE_OUTPUT_DIR="${OUTPUT_DIR}/temp_analysis_${TIMESTAMP}"
BATCH_SIZE=512  # Larger batch size for speed (balanced with memory)
NUM_EPOCHS=1
LEARNING_RATE=1e-3  # Slightly higher LR for faster convergence
DEVICE="cuda"
WANDB_PROJECT="GraphLLM4CTR_Hyperparameter_Temperature_Amazon_contrastive_FAST"
DATASET_TYPE="amazon"

# Create base output directory
mkdir -p "${BASE_OUTPUT_DIR}"

# Log file
LOG_FILE="${BASE_OUTPUT_DIR}/temperature_analysis_FAST.log"
touch "${LOG_FILE}"

echo "=========================================="
echo "Amazon Dataset Contrastive Learning"
echo "Start time: $(date)"
echo "Data directory: ${DATA_DIR}"
echo "Output directory: ${BASE_OUTPUT_DIR}"
echo "=========================================="

# Temperature values to analyze (start with one optimal value)
TEMPERATURES=(0.1 0.2 0.5 0.7)

# Run for each temperature
for TEMP in "${TEMPERATURES[@]}"; do
    echo "=========================================="
    echo "Analyzing Temperature: ${TEMP}"
    echo "Start time: $(date)"
    echo "=========================================="

    # Create directory for this temperature
    TEMP_DIR="${BASE_OUTPUT_DIR}/temp_${TEMP}"
    mkdir -p "${TEMP_DIR}"

    # Run ultra-fast contrastive learning
    python contrastive_learning_train_for_check.py \
        --data_dir "${DATA_DIR}" \
        --model_save_dir "${TEMP_DIR}" \
        --batch_size "${BATCH_SIZE}" \
        --num_epochs "${NUM_EPOCHS}" \
        --learning_rate "${LEARNING_RATE}" \
        --device "${DEVICE}" \
        --temperature "${TEMP}" \
        --wandb_project "${WANDB_PROJECT}" \
        --dataset_type "${DATASET_TYPE}" 2>&1 | tee -a "${LOG_FILE}"

    # Check if the run was successful
    if [ $? -eq 0 ]; then
        echo "Temperature ${TEMP} completed successfully at $(date)" | tee -a "${LOG_FILE}"
    else
        echo "Temperature ${TEMP} failed at $(date)" | tee -a "${LOG_FILE}"
    fi

    echo "End time: $(date)"
    echo "=========================================="
done

echo "=========================================="
echo "All temperature analysis completed!"
echo "End time: $(date)"
echo "Results saved in: ${BASE_OUTPUT_DIR}"
echo "Log file: ${LOG_FILE}"
echo "=========================================="

# Display summary
echo ""
echo "Summary of results:"
for TEMP in "${TEMPERATURES[@]}"; do
    TEMP_DIR="${BASE_OUTPUT_DIR}/temp_${TEMP}"
    if [ -d "${TEMP_DIR}" ]; then
        echo "  Temperature ${TEMP}: $(ls -la ${TEMP_DIR} | wc -l) files created"
        if [ -f "${TEMP_DIR}/contrastive_model.pt" ]; then
            echo "    ✓ Model saved successfully"
        else
            echo "    ✗ Model not found"
        fi
    else
        echo "  Temperature ${TEMP}: Directory not found"
    fi
done
