{"time":"2025-05-28T12:56:35.586974407Z","level":"INFO","msg":"stream: starting","core version":"0.19.10","symlink path":"/root/code/GraphLLM4CTR/models/wandb/run-20250528_125635-msi4r8y0/logs/debug-core.log"}
{"time":"2025-05-28T12:56:36.283654519Z","level":"INFO","msg":"created new stream","id":"msi4r8y0"}
{"time":"2025-05-28T12:56:36.283687275Z","level":"INFO","msg":"stream: started","id":"msi4r8y0"}
{"time":"2025-05-28T12:56:36.283700055Z","level":"INFO","msg":"writer: Do: started","stream_id":"msi4r8y0"}
{"time":"2025-05-28T12:56:36.283718096Z","level":"INFO","msg":"handler: started","stream_id":"msi4r8y0"}
{"time":"2025-05-28T12:56:36.28373718Z","level":"INFO","msg":"sender: started","stream_id":"msi4r8y0"}
{"time":"2025-05-28T12:56:36.692038886Z","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-28T13:30:29.369077604Z","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-28T13:30:29.369120371Z","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-28T13:30:30.3705788Z","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading wandb-summary.json","runtime_seconds":0.477488709,"progress":"1.0KB/1.0KB"},{"desc":"uploading output.log","runtime_seconds":0.477478774,"progress":"64.0KB/615.1KB"},{"desc":"uploading config.yaml","runtime_seconds":0.169761653,"progress":"849B/849B"}],"total_operations":3}}
{"time":"2025-05-28T13:30:31.521733856Z","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-28T13:30:32.156988823Z","level":"INFO","msg":"stream: closing","id":"msi4r8y0"}
{"time":"2025-05-28T13:30:32.157005614Z","level":"INFO","msg":"handler: closed","stream_id":"msi4r8y0"}
{"time":"2025-05-28T13:30:32.157013554Z","level":"INFO","msg":"writer: Close: closed","stream_id":"msi4r8y0"}
{"time":"2025-05-28T13:30:32.157026714Z","level":"INFO","msg":"sender: closed","stream_id":"msi4r8y0"}
{"time":"2025-05-28T13:30:32.157062954Z","level":"INFO","msg":"stream: closed","id":"msi4r8y0"}
