{"time":"2025-05-29T07:58:16.220044667Z","level":"INFO","msg":"stream: starting","core version":"0.19.10","symlink path":"/root/code/GraphLLM4CTR/models/wandb/run-20250529_075816-2km8pemj/logs/debug-core.log"}
{"time":"2025-05-29T07:58:17.090678099Z","level":"INFO","msg":"created new stream","id":"2km8pemj"}
{"time":"2025-05-29T07:58:17.090710005Z","level":"INFO","msg":"stream: started","id":"2km8pemj"}
{"time":"2025-05-29T07:58:17.090741812Z","level":"INFO","msg":"handler: started","stream_id":"2km8pemj"}
{"time":"2025-05-29T07:58:17.090741748Z","level":"INFO","msg":"writer: Do: started","stream_id":"2km8pemj"}
{"time":"2025-05-29T07:58:17.090829077Z","level":"INFO","msg":"sender: started","stream_id":"2km8pemj"}
{"time":"2025-05-29T07:58:17.419382868Z","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-29T08:10:47.717971257Z","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-05-29T08:57:02.927857339Z","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": read tcp 192.168.255.112:52520->35.186.228.49:443: read: connection reset by peer"}
{"time":"2025-05-29T08:59:32.919339568Z","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": read tcp 192.168.255.112:34394->35.186.228.49:443: read: connection reset by peer"}
