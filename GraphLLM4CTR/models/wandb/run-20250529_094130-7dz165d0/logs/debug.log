2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_setup.py:_flush():68] Current SDK version is 0.19.10
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_setup.py:_flush():68] Configure stats pid to 202686
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_setup.py:_flush():68] Loading settings from /root/.config/wandb/settings
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_setup.py:_flush():68] Loading settings from /root/code/GraphLLM4CTR/models/wandb/settings
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/GraphLLM4CTR/models/wandb/run-20250529_094130-7dz165d0/logs/debug.log
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/GraphLLM4CTR/models/wandb/run-20250529_094130-7dz165d0/logs/debug-internal.log
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_init.py:init():852] calling init triggers
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'learning_rate': 0.001, 'batch_size': 512, 'epochs': 1, 'model': 'ContrastiveLearner', 'device': 'cuda', 'dataset_type': 'amazon', '_wandb': {}}
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_init.py:init():893] starting backend
2025-05-29 09:41:30,200 INFO    MainThread:202686 [wandb_init.py:init():897] sending inform_init request
2025-05-29 09:41:30,221 INFO    MainThread:202686 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-29 09:41:30,221 INFO    MainThread:202686 [wandb_init.py:init():907] backend started and connected
2025-05-29 09:41:30,223 INFO    MainThread:202686 [wandb_init.py:init():1002] updated telemetry
2025-05-29 09:41:30,240 INFO    MainThread:202686 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
2025-05-29 09:41:31,279 INFO    MainThread:202686 [wandb_init.py:init():1101] starting run threads in backend
2025-05-29 09:41:31,408 INFO    MainThread:202686 [wandb_run.py:_console_start():2566] atexit reg
2025-05-29 09:41:31,409 INFO    MainThread:202686 [wandb_run.py:_redirect():2414] redirect: wrap_raw
2025-05-29 09:41:31,409 INFO    MainThread:202686 [wandb_run.py:_redirect():2483] Wrapping output streams.
2025-05-29 09:41:31,409 INFO    MainThread:202686 [wandb_run.py:_redirect():2506] Redirects installed.
2025-05-29 09:41:31,411 INFO    MainThread:202686 [wandb_init.py:init():1147] run started, returning control to user process
2025-05-29 09:42:22,411 INFO    MsgRouterThr:202686 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
