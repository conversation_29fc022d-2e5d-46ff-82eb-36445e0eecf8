2025-05-29 15:46:47,142 INFO    MainThread:260150 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/GraphLLM4CTR/models/wandb/run-20250529_154647-naz38fur/logs/debug.log
2025-05-29 15:46:47,142 INFO    MainThread:260150 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/GraphLLM4CTR/models/wandb/run-20250529_154647-naz38fur/logs/debug-internal.log
2025-05-29 15:46:47,142 INFO    MainThread:260150 [wandb_init.py:init():852] calling init triggers
2025-05-29 15:46:47,142 INFO    MainThread:260150 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-05-29 15:46:47,142 INFO    MainThread:260150 [wandb_init.py:init():893] starting backend
2025-05-29 15:46:47,142 INFO    MainThread:260150 [wandb_init.py:init():897] sending inform_init request
2025-05-29 15:46:47,143 INFO    MainThread:260150 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-29 15:46:47,143 INFO    MainThread:260150 [wandb_init.py:init():907] backend started and connected
2025-05-29 15:46:47,145 INFO    MainThread:260150 [wandb_init.py:init():1002] updated telemetry
2025-05-29 15:46:47,217 INFO    MainThread:260150 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
2025-05-29 15:46:48,113 INFO    MainThread:260150 [wandb_init.py:init():1101] starting run threads in backend
2025-05-29 15:46:48,240 INFO    MainThread:260150 [wandb_run.py:_console_start():2566] atexit reg
2025-05-29 15:46:48,240 INFO    MainThread:260150 [wandb_run.py:_redirect():2414] redirect: wrap_raw
2025-05-29 15:46:48,240 INFO    MainThread:260150 [wandb_run.py:_redirect():2483] Wrapping output streams.
2025-05-29 15:46:48,240 INFO    MainThread:260150 [wandb_run.py:_redirect():2506] Redirects installed.
2025-05-29 15:46:48,241 INFO    MainThread:260150 [wandb_init.py:init():1147] run started, returning control to user process
2025-05-29 15:46:48,241 INFO    MainThread:260150 [wandb_run.py:_config_callback():1429] config_cb None None {'embedding_size': 384, 'learning_rate': 0.0005, 'epochs': 1, 'batch_size': 128, 'weight_decay': 1e-05, 'early_stop_patience': 5, 'warmup_steps': 500, 'max_grad_norm': 1.0, 'seed': 42}
