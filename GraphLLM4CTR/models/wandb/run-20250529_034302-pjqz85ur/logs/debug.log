2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_setup.py:_flush():68] Current SDK version is 0.19.10
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_setup.py:_flush():68] Configure stats pid to 53346
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_setup.py:_flush():68] Loading settings from /root/.config/wandb/settings
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_setup.py:_flush():68] Loading settings from /root/code/GraphLLM4CTR/models/wandb/settings
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/GraphLLM4CTR/models/wandb/run-20250529_034302-pjqz85ur/logs/debug.log
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/GraphLLM4CTR/models/wandb/run-20250529_034302-pjqz85ur/logs/debug-internal.log
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_init.py:init():852] calling init triggers
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_init.py:init():893] starting backend
2025-05-29 03:43:02,588 INFO    MainThread:53346 [wandb_init.py:init():897] sending inform_init request
2025-05-29 03:43:02,610 INFO    MainThread:53346 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-29 03:43:02,610 INFO    MainThread:53346 [wandb_init.py:init():907] backend started and connected
2025-05-29 03:43:02,613 INFO    MainThread:53346 [wandb_init.py:init():1002] updated telemetry
2025-05-29 03:43:02,662 INFO    MainThread:53346 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
2025-05-29 03:43:03,613 INFO    MainThread:53346 [wandb_init.py:init():1101] starting run threads in backend
2025-05-29 03:43:03,738 INFO    MainThread:53346 [wandb_run.py:_console_start():2566] atexit reg
2025-05-29 03:43:03,738 INFO    MainThread:53346 [wandb_run.py:_redirect():2414] redirect: wrap_raw
2025-05-29 03:43:03,738 INFO    MainThread:53346 [wandb_run.py:_redirect():2483] Wrapping output streams.
2025-05-29 03:43:03,738 INFO    MainThread:53346 [wandb_run.py:_redirect():2506] Redirects installed.
2025-05-29 03:43:03,740 INFO    MainThread:53346 [wandb_init.py:init():1147] run started, returning control to user process
2025-05-29 03:43:03,741 INFO    MainThread:53346 [wandb_run.py:_config_callback():1429] config_cb None None {'epochs': 3, 'batch_size': 128, 'learning_rate': 0.0005, 'weight_decay': 1e-05, 'early_stop_patience': 5, 'warmup_steps': 500, 'max_grad_norm': 1.0, 'seed': 42}
