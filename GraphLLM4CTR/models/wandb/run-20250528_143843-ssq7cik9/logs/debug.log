2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_setup.py:_flush():68] Current SDK version is 0.19.10
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_setup.py:_flush():68] Configure stats pid to 7041
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_setup.py:_flush():68] Loading settings from /root/.config/wandb/settings
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_setup.py:_flush():68] Loading settings from /root/code/GraphLLM4CTR/models/wandb/settings
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/GraphLLM4CTR/models/wandb/run-20250528_143843-ssq7cik9/logs/debug.log
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/GraphLLM4CTR/models/wandb/run-20250528_143843-ssq7cik9/logs/debug-internal.log
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_init.py:init():852] calling init triggers
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'learning_rate': 0.001, 'batch_size': 512, 'epochs': 1, 'model': 'ContrastiveLearner', 'device': 'cuda', 'dataset_type': 'amazon', '_wandb': {}}
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_init.py:init():893] starting backend
2025-05-28 14:38:43,557 INFO    MainThread:7041 [wandb_init.py:init():897] sending inform_init request
2025-05-28 14:38:43,578 INFO    MainThread:7041 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-28 14:38:43,578 INFO    MainThread:7041 [wandb_init.py:init():907] backend started and connected
2025-05-28 14:38:43,580 INFO    MainThread:7041 [wandb_init.py:init():1002] updated telemetry
2025-05-28 14:38:43,597 INFO    MainThread:7041 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
2025-05-28 14:38:44,736 INFO    MainThread:7041 [wandb_init.py:init():1101] starting run threads in backend
2025-05-28 14:38:44,865 INFO    MainThread:7041 [wandb_run.py:_console_start():2566] atexit reg
2025-05-28 14:38:44,865 INFO    MainThread:7041 [wandb_run.py:_redirect():2414] redirect: wrap_raw
2025-05-28 14:38:44,866 INFO    MainThread:7041 [wandb_run.py:_redirect():2483] Wrapping output streams.
2025-05-28 14:38:44,866 INFO    MainThread:7041 [wandb_run.py:_redirect():2506] Redirects installed.
2025-05-28 14:38:44,867 INFO    MainThread:7041 [wandb_init.py:init():1147] run started, returning control to user process
2025-05-28 15:12:26,731 INFO    MainThread:7041 [wandb_run.py:_finish():2314] finishing run 9petrestaurant-huazhong-university-of-science-and-technology/GraphLLM4CTR_Hyperparameter_Temperature_Amazon_contrastive_FAST/ssq7cik9
2025-05-28 15:12:26,735 INFO    MainThread:7041 [wandb_run.py:_atexit_cleanup():2531] got exitcode: 0
2025-05-28 15:12:26,737 INFO    MainThread:7041 [wandb_run.py:_restore():2513] restore
2025-05-28 15:12:26,737 INFO    MainThread:7041 [wandb_run.py:_restore():2519] restore done
2025-05-28 15:12:29,681 INFO    MainThread:7041 [wandb_run.py:_footer_history_summary_info():4160] rendering history
2025-05-28 15:12:29,681 INFO    MainThread:7041 [wandb_run.py:_footer_history_summary_info():4192] rendering summary
2025-05-28 15:12:29,683 INFO    MainThread:7041 [wandb_run.py:_footer_sync_info():4121] logging synced files
