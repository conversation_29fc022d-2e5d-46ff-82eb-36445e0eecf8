2025-05-30 01:56:00,118 INFO    MainThread:2327 [wandb_setup.py:_flush():68] Current SDK version is 0.19.10
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_setup.py:_flush():68] Configure stats pid to 2327
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_setup.py:_flush():68] Loading settings from /root/.config/wandb/settings
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_setup.py:_flush():68] Loading settings from /root/code/GraphLLM4CTR/models/wandb/settings
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/GraphLLM4CTR/models/wandb/run-20250530_015600-2gvruqi0/logs/debug.log
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/GraphLLM4CTR/models/wandb/run-20250530_015600-2gvruqi0/logs/debug-internal.log
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_init.py:init():852] calling init triggers
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_init.py:init():893] starting backend
2025-05-30 01:56:00,119 INFO    MainThread:2327 [wandb_init.py:init():897] sending inform_init request
2025-05-30 01:56:00,141 INFO    MainThread:2327 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-30 01:56:00,142 INFO    MainThread:2327 [wandb_init.py:init():907] backend started and connected
2025-05-30 01:56:00,145 INFO    MainThread:2327 [wandb_init.py:init():1002] updated telemetry
2025-05-30 01:56:00,177 INFO    MainThread:2327 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
