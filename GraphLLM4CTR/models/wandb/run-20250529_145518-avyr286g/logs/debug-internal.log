{"time":"2025-05-29T14:55:18.657172486Z","level":"INFO","msg":"stream: starting","core version":"0.19.10","symlink path":"/root/code/GraphLLM4CTR/models/wandb/run-20250529_145518-avyr286g/logs/debug-core.log"}
{"time":"2025-05-29T14:55:19.349925156Z","level":"INFO","msg":"created new stream","id":"avyr286g"}
{"time":"2025-05-29T14:55:19.349956796Z","level":"INFO","msg":"stream: started","id":"avyr286g"}
{"time":"2025-05-29T14:55:19.34997517Z","level":"INFO","msg":"writer: Do: started","stream_id":"avyr286g"}
{"time":"2025-05-29T14:55:19.349990965Z","level":"INFO","msg":"handler: started","stream_id":"avyr286g"}
{"time":"2025-05-29T14:55:19.350018536Z","level":"INFO","msg":"sender: started","stream_id":"avyr286g"}
{"time":"2025-05-29T14:55:19.741408514Z","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-29T15:46:43.079870352Z","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-29T15:46:43.079921523Z","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-29T15:46:44.081145203Z","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading output.log","runtime_seconds":0.453472808},{"desc":"uploading wandb-summary.json","runtime_seconds":0.453465276},{"desc":"uploading config.yaml","runtime_seconds":0.253379818}],"total_operations":3}}
{"time":"2025-05-29T15:46:44.66995023Z","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-29T15:46:45.386523559Z","level":"INFO","msg":"stream: closing","id":"avyr286g"}
{"time":"2025-05-29T15:46:45.386545802Z","level":"INFO","msg":"handler: closed","stream_id":"avyr286g"}
{"time":"2025-05-29T15:46:45.386553956Z","level":"INFO","msg":"writer: Close: closed","stream_id":"avyr286g"}
{"time":"2025-05-29T15:46:45.386565703Z","level":"INFO","msg":"sender: closed","stream_id":"avyr286g"}
{"time":"2025-05-29T15:46:45.386607907Z","level":"INFO","msg":"stream: closed","id":"avyr286g"}
