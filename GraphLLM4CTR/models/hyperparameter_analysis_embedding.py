import os
import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, get_linear_schedule_with_warmup
from tqdm import tqdm
import wandb
import argparse
import random
import json
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score
from datetime import datetime
import time

from embedding_loader import EmbeddingLoader
from expert_fusion_focused import FocusedHybridExpertAdaptor
from llm_rgcn_layer_res import ResLLMRGCNEncoder
from llm_hgt_layer_res import ResLLMHGTEncoder
from graph_input_builder import build_rgcn_input, build_hgt_input
from qformer import TextQFormer


class CTRDataset(Dataset):
    def __init__(self, df, dataset_type='movielens'):
        self.user_ids = df["user_id"].tolist()
        if dataset_type == 'movielens':
            item_column = 'movie_id'
        else:  # For amazon, bookcrossing, and other datasets
            item_column = 'item_id'

        self.item_ids = df[item_column].tolist()
        self.labels = df["label"].tolist()

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        return {
            "user_id": self.user_ids[idx],
            "item_id": self.item_ids[idx],
            "label": self.labels[idx]
        }


def evaluate(model, dataloader, loader, hgt, rgcn, device):
    model.eval()
    all_probs, all_labels = [], []
    with torch.no_grad():
        for batch in dataloader:
            user_ids = batch["user_id"]
            item_ids = batch["item_id"]
            labels = torch.tensor(batch["label"], dtype=torch.float32).to(device)

            x_dict, edge_index_hgt, edge_type_hgt, node_type = build_hgt_input(user_ids, item_ids, loader)
            hgt_out = hgt(x_dict, edge_index_hgt, edge_type_hgt, node_type)

            x_rgcn, edge_index_rgcn, edge_type_rgcn = build_rgcn_input(user_ids, item_ids, loader)
            rgcn_out = rgcn(x_rgcn, edge_index_rgcn, edge_type_rgcn)

            user_feat = hgt_out[:len(user_ids)] + rgcn_out[:len(user_ids)]
            item_feat = hgt_out[len(user_ids):] + rgcn_out[len(user_ids):]

            prob, _, _ = model(user_feat, item_feat)
            prob_squeezed = prob.squeeze()
            # Handle the case where squeeze() creates a 0-dimensional tensor
            if prob_squeezed.dim() == 0:
                prob_squeezed = prob_squeezed.unsqueeze(0)
            all_probs.append(prob_squeezed.cpu())
            all_labels.append(labels.cpu())

    probs = torch.cat(all_probs).numpy()
    labels = torch.cat(all_labels).numpy()

    # Convert probabilities to binary predictions for accuracy calculation
    preds = (probs >= 0.5).astype(int)

    # Validate data quality
    if len(probs) == 0:
        print("WARNING: Empty predictions in evaluation!")
        return {"auc": 0.5, "logloss": 1.0, "accuracy": 0.5}

    # Check for valid probability range
    if np.any(probs < 0) or np.any(probs > 1):
        print(f"WARNING: Invalid probabilities! Min: {probs.min():.4f}, Max: {probs.max():.4f}")
        probs = np.clip(probs, 1e-7, 1-1e-7)

    # Check for constant predictions (indicates model not learning)
    if len(np.unique(probs)) == 1:
        print(f"WARNING: All predictions are identical: {probs[0]:.4f}")

    # Clip probabilities to avoid log(0) in logloss
    probs_clipped = np.clip(probs, 1e-7, 1-1e-7)

    return {
        "auc": roc_auc_score(labels, probs),
        "logloss": log_loss(labels, probs_clipped),
        "accuracy": accuracy_score(labels, preds)
    }


def set_seed(seed):
    """Set random seed for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


def train_and_evaluate(
    train_path,
    val_path,
    test_path,
    embedding_dir,
    item_meta_path,
    text_qformer,
    tokenizer,
    qformer_ckpt,
    embedding_size,
    device="cuda",
    epochs=10,
    batch_size=256,
    lr=5e-4,
    weight_decay=1e-5,
    early_stop_patience=5,
    warmup_steps=500,
    max_grad_norm=1.0,
    seed=42,
    log_dir="/data/datasets/processed_datasets/amazon/embedding_analysis/embedding",
    use_wandb=True,
    dataset_type='amazon'
):
    # Set random seed for reproducibility
    set_seed(seed)

    # Create a unique run name based on embedding size
    run_name = f"embed_{embedding_size}"

    if use_wandb:
        wandb.init(project="GraphLLM4CTR_Hyperparameter_Embedding_Amazon", name=run_name, group="embedding_size_analysis")
        # Log hyperparameters
        wandb.config.update({
            "embedding_size": embedding_size,
            "learning_rate": lr,
            "epochs": epochs,
            "batch_size": batch_size,
            "weight_decay": weight_decay,
            "early_stop_patience": early_stop_patience,
            "warmup_steps": warmup_steps,
            "max_grad_norm": max_grad_norm,
            "seed": seed
        })

    # Create log directory for this specific embedding size
    embed_log_dir = os.path.join(log_dir, run_name)
    os.makedirs(embed_log_dir, exist_ok=True)

    # Configure for dataset type
    if dataset_type == 'amazon':
        item_id_col = "item_id"
        title_col = "title"
    elif dataset_type == 'bookcrossing':
        item_id_col = "item_id"
        title_col = "title"  # Assuming BookCrossing has title column
    else:
        item_id_col = "movie_id"
        title_col = "title"

    loader = EmbeddingLoader(embedding_dir, item_meta_path, item_id_col, title_col, text_qformer, qformer_ckpt, tokenizer, device)

    # Load and preprocess data
    train_df = pd.read_csv(train_path)
    train_df = train_df.sample(frac=1, random_state=seed).reset_index(drop=True)
    val_df = pd.read_csv(val_path)
    test_df = pd.read_csv(test_path)

    # Data validation: Check for feature variation
    print(f"Data validation for embedding_size={embedding_size}:")
    for col in train_df.columns:
        if col not in ['user_id', 'item_id', 'label']:
            unique_vals = train_df[col].nunique()
            print(f"  {col}: {unique_vals} unique values")
            if unique_vals == 1:
                print(f"  WARNING: {col} has no variation (all values are {train_df[col].iloc[0]})")

    # Check label distribution
    label_dist = train_df['label'].value_counts()
    print(f"  Label distribution: {dict(label_dist)}")
    print(f"  Label balance: {label_dist.min()/label_dist.max():.3f}")
    print()

    # Determine dataset type from embedding_dir path
    if 'amazon' in embedding_dir:
        dataset_type = 'amazon'
    elif 'bookcrossing' in embedding_dir:
        dataset_type = 'bookcrossing'
    else:
        dataset_type = 'movielens'

    train_loader = DataLoader(CTRDataset(train_df, dataset_type), batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(CTRDataset(val_df, dataset_type), batch_size=batch_size)
    test_loader = DataLoader(CTRDataset(test_df, dataset_type), batch_size=batch_size)

    # Calculate expert output dimension as half of embedding size
    expert_output_dim = max(32, embedding_size // 2)

    # Initialize models with the specified embedding size
    # HGT - User input dim is 256, Item input dim is 1024 (256 + 768)
    hgt = ResLLMHGTEncoder(
        input_dim=1024,
        hidden_dim=embedding_size,  # Variable embedding size
        num_layers=2,
        num_types=2,
        num_heads=min(4, max(1, embedding_size // 64)),  # Adjust heads based on embedding size
        dropout=0.1,
        device=device
    ).to(device)

    # R-GCN - Input dim is 256 for both user and item
    rgcn = ResLLMRGCNEncoder(
        input_dim=256,
        hidden_dim=embedding_size,  # Variable embedding size
        num_relations=2,
        num_layers=2,
        dropout=0.1,
        device=device
    ).to(device)

    # Expert fusion model
    model = FocusedHybridExpertAdaptor(
        input_dim=embedding_size,  # Match embedding size
        expert_output_dim=expert_output_dim,
        num_shared_experts=3,
        num_user_experts=3,
        num_item_experts=3,
        hidden_dim=expert_output_dim,  # Match expert output dim
        dropout=0.1
    ).to(device)

    # Combine all parameters for optimization
    all_params = list(model.parameters()) + list(hgt.parameters()) + list(rgcn.parameters())

    # Debug model initialization
    total_params = sum(p.numel() for p in all_params)
    print(f"Model initialization for embedding_size={embedding_size}:")
    print(f"  Total parameters: {total_params:,}")
    print(f"  Expert output dim: {expert_output_dim}")
    print(f"  HGT attention heads: {min(4, max(1, embedding_size // 64))}")

    # Print sample of initial parameters to verify different initialization
    sample_param = next(iter(model.parameters())).flatten()[:3]
    print(f"  Sample initial parameters: {sample_param.detach().cpu().numpy()}")
    print()

    # Optimizer
    optimizer = torch.optim.AdamW(
        all_params,
        lr=lr,
        weight_decay=weight_decay
    )

    # Learning rate scheduler with warmup
    total_steps = len(train_loader) * epochs
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=warmup_steps,
        num_training_steps=total_steps
    )

    # Loss function
    criterion = nn.BCELoss()

    # Initialize tracking variables
    best_auc = 0
    best_accuracy = 0
    patience_counter = 0

    # Track metrics for analysis
    epoch_metrics = {
        "train_loss": [],
        "val_auc": [],
        "val_logloss": [],
        "val_accuracy": [],
        "learning_rates": []
    }

    # Track training time
    start_time = time.time()

    # Training loop
    for epoch in range(epochs):
        model.train()
        hgt.train()
        rgcn.train()

        total_loss = 0
        batch_losses = []

        for batch_idx, batch in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")):
            user_ids = batch["user_id"]
            item_ids = batch["item_id"]
            labels = torch.tensor(batch["label"], dtype=torch.float32).to(device)

            # Get HGT embeddings
            x_dict, edge_index_hgt, edge_type_hgt, node_type = build_hgt_input(user_ids, item_ids, loader)
            hgt_out = hgt(x_dict, edge_index_hgt, edge_type_hgt, node_type)

            # Get RGCN embeddings
            x_rgcn, edge_index_rgcn, edge_type_rgcn = build_rgcn_input(user_ids, item_ids, loader)
            rgcn_out = rgcn(x_rgcn, edge_index_rgcn, edge_type_rgcn)

            # Extract user and item embeddings
            hgt_user_feat = hgt_out[:len(user_ids)]
            hgt_item_feat = hgt_out[len(user_ids):]
            rgcn_user_feat = rgcn_out[:len(user_ids)]
            rgcn_item_feat = rgcn_out[len(user_ids):]

            # Combine HGT and RGCN embeddings
            user_feat = hgt_user_feat + rgcn_user_feat
            item_feat = hgt_item_feat + rgcn_item_feat

            # Forward pass through the expert fusion model
            prob, _, _ = model(user_feat, item_feat)

            # Calculate loss
            loss = criterion(prob.squeeze(), labels)

            # Backward pass and optimization
            optimizer.zero_grad()
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(all_params, max_grad_norm)

            optimizer.step()
            scheduler.step()

            # Track loss
            batch_loss = loss.item()
            total_loss += batch_loss
            batch_losses.append(batch_loss)

            # Log to wandb every 10 batches
            if use_wandb and batch_idx % 10 == 0:
                wandb.log({
                    "batch": epoch * len(train_loader) + batch_idx,
                    "batch_loss": batch_loss,
                    "running_avg_loss": total_loss / (batch_idx + 1),
                    "learning_rate": scheduler.get_last_lr()[0]
                })

        # Evaluate on validation set
        val_metrics = evaluate(model, val_loader, loader, hgt, rgcn, device)

        # Calculate epoch metrics
        avg_epoch_loss = total_loss / len(train_loader)
        epoch_loss_std = np.std(batch_losses) if len(batch_losses) > 1 else 0

        # Track metrics for analysis
        epoch_metrics["train_loss"].append(avg_epoch_loss)
        epoch_metrics["val_auc"].append(val_metrics["auc"])
        epoch_metrics["val_logloss"].append(val_metrics["logloss"])
        epoch_metrics["val_accuracy"].append(val_metrics["accuracy"])
        epoch_metrics["learning_rates"].append(scheduler.get_last_lr()[0])

        # Log metrics
        if use_wandb:
            wandb.log({
                "epoch": epoch+1,
                "train_loss": avg_epoch_loss,
                "train_loss_std": epoch_loss_std,
                "val_auc": val_metrics["auc"],
                "val_logloss": val_metrics["logloss"],
                "val_accuracy": val_metrics["accuracy"]
            })

        print(f"[Epoch {epoch+1}/{epochs}] Train Loss: {avg_epoch_loss:.4f} (±{epoch_loss_std:.4f}) | "
              f"Val AUC: {val_metrics['auc']:.4f} | Val Accuracy: {val_metrics['accuracy']:.4f}")

        # Save best model based on AUC
        if val_metrics["auc"] > best_auc:
            best_auc = val_metrics["auc"]
            best_accuracy = val_metrics["accuracy"]
            patience_counter = 0

            # Save all model components
            torch.save({
                'model': model.state_dict(),
                'hgt': hgt.state_dict(),
                'rgcn': rgcn.state_dict(),
                'epoch': epoch,
                'val_auc': val_metrics["auc"],
                'val_accuracy': val_metrics["accuracy"]
            }, os.path.join(embed_log_dir, "best_model.pt"))

            print(f"✓ New best model saved! AUC: {best_auc:.4f}, Accuracy: {best_accuracy:.4f}")
        else:
            patience_counter += 1
            if patience_counter >= early_stop_patience:
                print(f"Early stopping at epoch {epoch+1}")
                break

    # Calculate total training time
    training_time = time.time() - start_time

    # Load best model for testing
    checkpoint = torch.load(os.path.join(embed_log_dir, "best_model.pt"))
    model.load_state_dict(checkpoint['model'])
    hgt.load_state_dict(checkpoint['hgt'])
    rgcn.load_state_dict(checkpoint['rgcn'])

    # Test the model
    test_metrics = evaluate(model, test_loader, loader, hgt, rgcn, device)
    print(f"\n[Test Results] AUC: {test_metrics['auc']:.4f} | Accuracy: {test_metrics['accuracy']:.4f} | LogLoss: {test_metrics['logloss']:.4f}")

    if use_wandb:
        wandb.log({
            "test_auc": test_metrics["auc"],
            "test_logloss": test_metrics["logloss"],
            "test_accuracy": test_metrics["accuracy"],
            "best_epoch": checkpoint['epoch'],
            "best_val_auc": checkpoint['val_auc'],
            "training_time": training_time,
            "model_size": sum(p.numel() for p in all_params)
        })
        wandb.finish()

    # Save metrics for analysis
    results = {
        "embedding_size": embedding_size,
        "expert_output_dim": expert_output_dim,
        "best_val_auc": best_auc,
        "best_val_accuracy": best_accuracy,
        "test_auc": test_metrics["auc"],
        "test_accuracy": test_metrics["accuracy"],
        "test_logloss": test_metrics["logloss"],
        "best_epoch": checkpoint['epoch'],
        "epoch_metrics": epoch_metrics,
        "early_stopped": patience_counter >= early_stop_patience,
        "training_time": training_time,
        "model_size": sum(p.numel() for p in all_params)
    }

    # Save results to JSON
    with open(os.path.join(embed_log_dir, "results.json"), 'w') as f:
        json.dump(results, f, indent=4)

    return results


def run_embedding_size_analysis(args):
    # Create main log directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = os.path.join(args.log_dir, f"embedding_analysis_{timestamp}")
    os.makedirs(log_dir, exist_ok=True)

    embedding_sizes = [64, 128, 256, 384, 512]

    # Initialize tokenizer (reuse across all experiments)
    tokenizer = AutoTokenizer.from_pretrained("/root/code/roberta-base")

    # Store results for all embedding sizes
    all_results = []

    # Run analysis for each embedding size
    for embed_size in embedding_sizes:
        print(f"\n{'='*50}")
        print(f"Analyzing Embedding Size: {embed_size}")
        print(f"{'='*50}\n")

        # Create a fresh TextQFormer instance for each embedding size
        text_qformer = TextQFormer(768, 768, 32, 8)

        results = train_and_evaluate(
            train_path=args.train_path,
            val_path=args.val_path,
            test_path=args.test_path,
            embedding_dir=args.embedding_dir,
            item_meta_path=args.item_meta_path,
            text_qformer=text_qformer,
            tokenizer=tokenizer,
            qformer_ckpt=args.qformer_ckpt,
            embedding_size=embed_size,
            device=args.device,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.lr,
            weight_decay=args.weight_decay,
            early_stop_patience=args.early_stop_patience,
            warmup_steps=args.warmup_steps,
            max_grad_norm=args.max_grad_norm,
            seed=args.seed,
            log_dir=log_dir,
            use_wandb=args.use_wandb,
            dataset_type=args.dataset_type
        )

        all_results.append(results)

    # Save all results to a single file
    with open(os.path.join(log_dir, "all_results.json"), 'w') as f:
        json.dump(all_results, f, indent=4)

    # Create summary plots
    create_summary_plots(all_results, log_dir)

    return all_results


def create_summary_plots(results, log_dir):
    # Extract data for plotting
    embedding_sizes = [r["embedding_size"] for r in results]
    val_aucs = [r["best_val_auc"] for r in results]
    test_aucs = [r["test_auc"] for r in results]
    test_loglosses = [r["test_logloss"] for r in results]
    training_times = [r["training_time"] for r in results]
    model_sizes = [r["model_size"] for r in results]

    # Create AUC vs Embedding Size plot
    plt.figure(figsize=(10, 6))
    plt.plot(embedding_sizes, test_aucs, 'o-', label='Test AUC')
    plt.plot(embedding_sizes, val_aucs, 's--', label='Validation AUC')
    plt.xlabel('Embedding Size')
    plt.ylabel('AUC')
    plt.title('AUC vs Embedding Size')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.savefig(os.path.join(log_dir, 'auc_vs_embedding.png'), dpi=300, bbox_inches='tight')

    # Create LogLoss vs Embedding Size plot
    plt.figure(figsize=(10, 6))
    plt.plot(embedding_sizes, test_loglosses, 'o-')
    plt.xlabel('Embedding Size')
    plt.ylabel('LogLoss')
    plt.title('Test LogLoss vs Embedding Size')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(log_dir, 'logloss_vs_embedding.png'), dpi=300, bbox_inches='tight')

    # Create Training Time vs Embedding Size plot
    plt.figure(figsize=(10, 6))
    plt.plot(embedding_sizes, training_times, 'o-')
    plt.xlabel('Embedding Size')
    plt.ylabel('Training Time (seconds)')
    plt.title('Training Time vs Embedding Size')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(log_dir, 'time_vs_embedding.png'), dpi=300, bbox_inches='tight')

    # Create Model Size vs Embedding Size plot
    plt.figure(figsize=(10, 6))
    plt.plot(embedding_sizes, model_sizes, 'o-')
    plt.xlabel('Embedding Size')
    plt.ylabel('Model Size (parameters)')
    plt.title('Model Size vs Embedding Size')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(log_dir, 'size_vs_embedding.png'), dpi=300, bbox_inches='tight')

    # Create Efficiency plot (AUC per parameter)
    efficiency = [auc / (size / 1e6) for auc, size in zip(test_aucs, model_sizes)]
    plt.figure(figsize=(10, 6))
    plt.plot(embedding_sizes, efficiency, 'o-')
    plt.xlabel('Embedding Size')
    plt.ylabel('Test AUC per Million Parameters')
    plt.title('Model Efficiency vs Embedding Size')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(log_dir, 'efficiency_vs_embedding.png'), dpi=300, bbox_inches='tight')

    # Create a summary table
    summary_df = pd.DataFrame({
        'Embedding Size': embedding_sizes,
        'Test AUC': test_aucs,
        'Test LogLoss': test_loglosses,
        'Validation AUC': val_aucs,
        'Training Time (s)': training_times,
        'Model Size (params)': model_sizes,
        'Efficiency (AUC/M params)': efficiency
    })

    summary_df.to_csv(os.path.join(log_dir, 'summary.csv'), index=False)

    print("\nAnalysis Summary:")
    print(summary_df)
    print(f"\nBest Embedding Size: {embedding_sizes[np.argmax(test_aucs)]}")
    print(f"Best Test AUC: {max(test_aucs):.4f}")
    print(f"Corresponding Test LogLoss: {test_loglosses[np.argmax(test_aucs)]:.4f}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--train_path", type=str, default='/data/datasets/processed_datasets/amazon/train.csv')
    parser.add_argument("--val_path", type=str, default='/data/datasets/processed_datasets/amazon/val.csv')
    parser.add_argument("--test_path", type=str, default='/data/datasets/processed_datasets/amazon/test.csv')
    parser.add_argument("--embedding_dir", type=str, default='/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/results/temp_0.5/aligned_embeddings_epoch1')
    parser.add_argument("--item_meta_path", type=str, default='/data/datasets/processed_datasets/amazon/train.csv')
    parser.add_argument("--qformer_ckpt", type=str, default='/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/results/temp_0.5/qformer_epoch1.pt')
    parser.add_argument("--epochs", type=int, default=1)
    parser.add_argument("--batch_size", type=int, default=128)
    parser.add_argument("--lr", type=float, default=5e-4)
    parser.add_argument("--weight_decay", type=float, default=1e-5)
    parser.add_argument("--early_stop_patience", type=int, default=5)
    parser.add_argument("--warmup_steps", type=int, default=500)
    parser.add_argument("--max_grad_norm", type=float, default=1.0)
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--log_dir", type=str, default='/data/datasets/processed_datasets/amazon/embedding_analysis/embedding')
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--dataset_type", type=str, default="amazon")
    parser.add_argument("--use_wandb", action="store_true")
    args = parser.parse_args()

    # Run embedding size analysis
    os.makedirs(args.log_dir, exist_ok=True)
    results = run_embedding_size_analysis(args)
