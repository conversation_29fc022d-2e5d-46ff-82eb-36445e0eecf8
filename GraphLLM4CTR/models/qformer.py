import math
import torch
import torch.nn as nn

class MultiHeadAttention(nn.Module):
    def __init__(self, hidden_dim, num_heads):
        super().__init__()
        self.num_attention_heads = num_heads
        self.attention_head_size = hidden_dim // num_heads
        self.all_head_size = self.num_attention_heads * self.attention_head_size

        self.query = nn.Linear(hidden_dim, self.all_head_size)
        self.key = nn.Linear(hidden_dim, self.all_head_size)
        self.value = nn.Linear(hidden_dim, self.all_head_size)
        self.output = nn.Linear(hidden_dim, hidden_dim)

    def transpose_for_scores(self, x):
        batch_size, seq_length, _ = x.size()
        new_x_shape = (batch_size, seq_length, self.num_attention_heads, self.attention_head_size)
        x = x.view(*new_x_shape)
        return x.permute(0, 2, 1, 3)

    def forward(self, query_states, key_value_states, attention_mask=None):
        """
        For self-attention: pass the same input for query_states and key_value_states
        For cross-attention: pass different inputs
        """
        query_layer = self.query(query_states)
        key_layer = self.key(key_value_states)
        value_layer = self.value(key_value_states)

        query_layer = self.transpose_for_scores(query_layer)
        key_layer = self.transpose_for_scores(key_layer)
        value_layer = self.transpose_for_scores(value_layer)

        attention_scores = torch.matmul(query_layer, key_layer.transpose(-1, -2))
        attention_scores = attention_scores / math.sqrt(self.attention_head_size)

        if attention_mask is not None:
            extended_mask = attention_mask[:, None, None, :]
            attention_scores = attention_scores.masked_fill(extended_mask == 0, float('-inf'))

        attention_probs = nn.functional.softmax(attention_scores, dim=-1)
        context_layer = torch.matmul(attention_probs, value_layer)

        context_layer = context_layer.permute(0, 2, 1, 3).contiguous()
        new_context_layer_shape = context_layer.size()[:-2] + (self.all_head_size,)
        context_layer = context_layer.view(*new_context_layer_shape)

        output = self.output(context_layer)
        return output

class TransformerLayer(nn.Module):
    def __init__(self, hidden_dim, num_heads):
        super().__init__()
        self.self_attn = MultiHeadAttention(hidden_dim, num_heads)
        self.cross_attn = MultiHeadAttention(hidden_dim, num_heads)
        self.mlp = nn.Sequential(
            nn.Linear(hidden_dim, 4 * hidden_dim),
            nn.GELU(),
            nn.Linear(4 * hidden_dim, hidden_dim)
        )
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        self.norm3 = nn.LayerNorm(hidden_dim)

    def forward(self, hidden_states, encoder_hidden_states, attention_mask=None):
        # Self attention (first)
        residual = hidden_states
        hidden_states = self.norm1(hidden_states)
        hidden_states = self.self_attn(hidden_states, hidden_states)
        hidden_states = residual + hidden_states

        # Cross attention (second)
        residual = hidden_states
        hidden_states = self.norm2(hidden_states)
        hidden_states = self.cross_attn(hidden_states, encoder_hidden_states, attention_mask)
        hidden_states = residual + hidden_states

        # MLP (third)
        residual = hidden_states
        hidden_states = self.norm3(hidden_states)
        hidden_states = self.mlp(hidden_states)
        hidden_states = residual + hidden_states

        return hidden_states

class QFormer(nn.Module):
    def __init__(self, input_dim, hidden_dim=768, num_layers=6, num_heads=8, num_queries=32):
        super().__init__()
        self.query_embeddings = nn.Parameter(torch.randn(1, num_queries, hidden_dim))
        self.layers = nn.ModuleList([
            TransformerLayer(hidden_dim, num_heads) for _ in range(num_layers)
        ])
        self.hidden_dim = hidden_dim

    def forward(self, inputs, attention_mask=None):
        batch_size = inputs.shape[0]

        # Print shape for debugging
        # print(f"QFormer input shape: {inputs.shape}")

        # Expand query embeddings for batch size
        query_embeddings = self.query_embeddings.expand(batch_size, -1, -1)
        hidden_states = query_embeddings

        # Process through transformer layers
        for layer in self.layers:
            hidden_states = layer(hidden_states, inputs, attention_mask)

        return hidden_states

class GraphQFormer(nn.Module):
    def __init__(self, graph_embedding_dim, hidden_dim=768, num_layers=6, num_heads=8, num_queries=32):
        super().__init__()
        self.input_proj_144 = nn.Linear(144, hidden_dim)
        self.input_proj_128 = nn.Linear(128, hidden_dim)
        self.input_proj_96 = nn.Linear(96, hidden_dim)
        self.input_proj_80 = nn.Linear(80, hidden_dim)  # Add support for Amazon user embeddings

        self.qformer = QFormer(
            input_dim=hidden_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            num_heads=num_heads,
            num_queries=num_queries
        )
        self.graph_proj = nn.Linear(hidden_dim, hidden_dim)

    def forward(self, graph_embeddings, attention_mask=None):
        # Ensure input is 3D: [batch_size, seq_len, hidden_dim]
        if len(graph_embeddings.shape) == 2:
            graph_embeddings = graph_embeddings.unsqueeze(1)

        # Print shape for debugging
        # print(f"GraphQFormer Input shape: {graph_embeddings.shape}")

        # Choose appropriate projection based on input dimension
        if graph_embeddings.shape[-1] == 144:
            projected_inputs = self.input_proj_144(graph_embeddings)
        elif graph_embeddings.shape[-1] == 128:
            projected_inputs = self.input_proj_128(graph_embeddings)
        elif graph_embeddings.shape[-1] == 96:
            projected_inputs = self.input_proj_96(graph_embeddings)
        elif graph_embeddings.shape[-1] == 80:
            projected_inputs = self.input_proj_80(graph_embeddings)
        else:
            raise ValueError(f"Unexpected input dimension: {graph_embeddings.shape[-1]}")

        hidden_states = self.qformer(projected_inputs, attention_mask=attention_mask)
        projected = self.graph_proj(hidden_states)
        return projected

class TextQFormer(nn.Module):
    def __init__(self, text_embedding_dim, hidden_dim=768, num_layers=6, num_heads=12, num_queries=32):
        super().__init__()
        # Add input projection for text embeddings
        self.input_proj = nn.Linear(text_embedding_dim, hidden_dim)

        self.qformer = QFormer(
            input_dim=hidden_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            num_heads=num_heads,
            num_queries=num_queries
        )

        self.text_proj = nn.Linear(hidden_dim, hidden_dim)

    def forward(self, text_embeddings, attention_mask=None):
        # Ensure input is 3D: [batch_size, seq_len, hidden_dim]
        if len(text_embeddings.shape) == 2:
            text_embeddings = text_embeddings.unsqueeze(1)

        # Project input embeddings
        projected_inputs = self.input_proj(text_embeddings)

        hidden_states = self.qformer(projected_inputs, attention_mask=attention_mask)
        projected = self.text_proj(hidden_states)
        return projected
