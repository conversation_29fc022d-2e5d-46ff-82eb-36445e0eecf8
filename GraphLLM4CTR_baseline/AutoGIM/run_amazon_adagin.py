
import sys
sys.path.append('GraphLLM4CTR_baseline/AutoGIM')

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score
import numpy as np
from tqdm import tqdm
import json

class AmazonAdaGINDataset(Dataset):
    def __init__(self, csv_file, feature_vocab):
        self.data = pd.read_csv(csv_file)
        self.feature_vocab = feature_vocab
        
        # Prepare features
        self.features = []
        self.labels = self.data['label'].values
        
        for _, row in self.data.iterrows():
            feature_vector = []
            
            # Categorical features
            for col in ['user_id', 'item_id', 'brand_index', 'price_range_index', 
                       'category_1', 'category_2', 'category_3']:
                feature_vector.append(int(row[col]))
            
            # Numeric feature (price)
            feature_vector.append(float(row['price']))
            
            self.features.append(feature_vector)
        
        self.features = np.array(self.features)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return {
            'features': torch.tensor(self.features[idx], dtype=torch.float32),
            'label': torch.tensor(self.labels[idx], dtype=torch.float32)
        }

class SimpleAdaGIN(nn.Module):
    def __init__(self, feature_dims, embedding_dim=16):
        super().__init__()
        
        # Embedding layers for categorical features
        self.embeddings = nn.ModuleList()
        for dim in feature_dims:
            self.embeddings.append(nn.Embedding(dim + 1, embedding_dim))
        
        # MLP for final prediction
        total_dim = len(feature_dims) * embedding_dim + 1  # +1 for price
        self.mlp = nn.Sequential(
            nn.Linear(total_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1)
        )
    
    def forward(self, features):
        embedded_features = []
        
        # Embed categorical features
        for i, embedding in enumerate(self.embeddings):
            cat_feature = features[:, i].long()
            embedded = embedding(cat_feature)
            embedded_features.append(embedded)
        
        # Add numeric feature (price)
        price = features[:, -1:]
        embedded_features.append(price)
        
        # Concatenate all features
        x = torch.cat(embedded_features, dim=1)
        
        # Final prediction
        output = self.mlp(x)
        return output.squeeze()

# Load feature vocabulary
with open('GraphLLM4CTR_baseline/AutoGIM/data/amazon/feature_vocab.json', 'r') as f:
    feature_vocab = json.load(f)

# Create datasets
train_dataset = AmazonAdaGINDataset('GraphLLM4CTR_baseline/AutoGIM/data/amazon/train.csv', feature_vocab)
val_dataset = AmazonAdaGINDataset('GraphLLM4CTR_baseline/AutoGIM/data/amazon/valid.csv', feature_vocab)
test_dataset = AmazonAdaGINDataset('GraphLLM4CTR_baseline/AutoGIM/data/amazon/test.csv', feature_vocab)

# Create dataloaders
train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=512)
test_loader = DataLoader(test_dataset, batch_size=512)

# Initialize model
feature_dims = [feature_vocab[col]['max_value'] for col in 
               ['user_id', 'item_id', 'brand_index', 'price_range_index', 
                'category_1', 'category_2', 'category_3']]

model = SimpleAdaGIN(feature_dims)
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

# Training setup
optimizer = optim.Adam(model.parameters(), lr=0.001)
criterion = nn.BCEWithLogitsLoss()

print("Training AdaGIN on Amazon...")
for epoch in range(5):
    model.train()
    total_loss = 0
    
    for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
        features = batch['features'].to(device)
        labels = batch['label'].to(device)
        
        outputs = model(features)
        loss = criterion(outputs, labels)
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
    
    print(f"Epoch {epoch+1} Loss: {total_loss/len(train_loader):.4f}")

# Evaluation
model.eval()
all_preds = []
all_labels = []

with torch.no_grad():
    for batch in tqdm(test_loader, desc="Evaluating"):
        features = batch['features'].to(device)
        labels = batch['label'].cpu().numpy()
        
        outputs = model(features)
        preds = torch.sigmoid(outputs).cpu().numpy()
        
        all_preds.extend(preds)
        all_labels.extend(labels)

# Calculate metrics
all_preds = np.array(all_preds)
all_labels = np.array(all_labels)
auc = roc_auc_score(all_labels, all_preds)
logloss = log_loss(all_labels, all_preds)
accuracy = accuracy_score(all_labels, (all_preds >= 0.5).astype(int))

print(f"AdaGIN Amazon Results:")
print(f"Test AUC: {auc:.4f}")
print(f"Test Logloss: {logloss:.4f}")
print(f"Test Accuracy: {accuracy:.4f}")

# Save results
results = {
    'model': 'AdaGIN',
    'dataset': 'Amazon',
    'test_auc': float(auc),
    'test_logloss': float(logloss),
    'test_accuracy': float(accuracy)
}

with open('GraphLLM4CTR_baseline/AutoGIM/amazon_results.json', 'w') as f:
    json.dump(results, f, indent=2)
