
import sys
sys.path.append('/data/p5_model')

import pandas as pd
import torch
from transformers import T5Tokenizer, T5ForConditionalGeneration
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, log_loss
import numpy as np
from tqdm import tqdm

class AmazonP5Dataset(Dataset):
    def __init__(self, csv_file, tokenizer, max_length=128):
        self.data = pd.read_csv(csv_file)
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        prompt = row['prompt']
        target = row['target']
        label = row['label']

        # Tokenize
        inputs = self.tokenizer(prompt, max_length=self.max_length,
                               padding='max_length', truncation=True, return_tensors='pt')
        targets = self.tokenizer(target, max_length=10,
                                padding='max_length', truncation=True, return_tensors='pt')

        return {
            'input_ids': inputs.input_ids.squeeze(),
            'attention_mask': inputs.attention_mask.squeeze(),
            'labels': targets.input_ids.squeeze(),
            'raw_label': torch.tensor(label, dtype=torch.float)
        }

# Load tokenizer and model
tokenizer = T5Tokenizer.from_pretrained('/data/p5_model')
model = T5ForConditionalGeneration.from_pretrained('/data/p5_model')

# Load data
train_dataset = AmazonP5Dataset('GraphLLM4CTR_baseline/P5/data/amazon/train.csv', tokenizer)
val_dataset = AmazonP5Dataset('GraphLLM4CTR_baseline/P5/data/amazon/val.csv', tokenizer)
test_dataset = AmazonP5Dataset('GraphLLM4CTR_baseline/P5/data/amazon/test.csv', tokenizer)

# Create dataloaders
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=16)
test_loader = DataLoader(test_dataset, batch_size=16)

# Simple training loop
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)
optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)

print("Training P5 on Amazon...")
for epoch in range(3):
    model.train()
    total_loss = 0

    for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        labels = batch['labels'].to(device)

        outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
        loss = outputs.loss

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        total_loss += loss.item()

    print(f"Epoch {epoch+1} Loss: {total_loss/len(train_loader):.4f}")

# Simple evaluation
model.eval()
all_preds = []
all_labels = []

with torch.no_grad():
    for batch in tqdm(test_loader, desc="Evaluating"):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        raw_labels = batch['raw_label'].numpy()

        # Generate predictions
        outputs = model.generate(input_ids=input_ids, attention_mask=attention_mask,
                                max_length=10, num_return_sequences=1)

        # Decode predictions
        for i, output in enumerate(outputs):
            pred_text = tokenizer.decode(output, skip_special_tokens=True).strip().lower()
            pred_prob = 0.7 if 'yes' in pred_text else 0.3  # Simple heuristic
            all_preds.append(pred_prob)
            all_labels.append(raw_labels[i])

# Calculate metrics
all_preds = np.array(all_preds)
all_labels = np.array(all_labels)
auc = roc_auc_score(all_labels, all_preds)
logloss = log_loss(all_labels, all_preds)

print(f"P5 Amazon Results:")
print(f"Test AUC: {auc:.4f}")
print(f"Test Logloss: {logloss:.4f}")

# Save results
results = {
    'model': 'P5',
    'dataset': 'Amazon',
    'test_auc': float(auc),
    'test_logloss': float(logloss)
}

import json
with open('GraphLLM4CTR_baseline/P5/amazon_results.json', 'w') as f:
    json.dump(results, f, indent=2)
