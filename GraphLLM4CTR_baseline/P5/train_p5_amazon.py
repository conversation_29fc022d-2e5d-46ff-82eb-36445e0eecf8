#!/usr/bin/env python3
"""
Training script for P5 on Amazon dataset for CTR prediction.
"""
import os
import sys
import json
import argparse
import logging
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, log_loss
from tqdm import tqdm
import wandb

# Add the P5 model directory to the path
sys.path.append('/data/p5_model')

# Import P5 model components
from modeling_p5 import P5
from transformers import T5Config, T5Tokenizer

# Set up logging
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


class AmazonP5Dataset(Dataset):
    """Dataset for P5 CTR prediction on Amazon data"""
    def __init__(self, data_path, tokenizer, max_length=128, mode='train'):
        self.data = pd.read_csv(data_path)
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.mode = mode

        # Extract user features (Amazon specific)
        self.user_features = {
            'user_id': self.data['user_id'].values
        }

        # Extract item features (Amazon specific)
        self.item_features = {
            'item_id': self.data['item_id'].values,
            'brand_index': self.data['brand_index'].values,
            'price_range_index': self.data['price_range_index'].values,
            'category_1': self.data['category_1'].values,
            'category_2': self.data['category_2'].values,
            'category_3': self.data['category_3'].values
        }

        # Extract labels
        self.labels = self.data['label'].values

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        user_id = self.user_features['user_index'][idx]
        item_id = self.item_features['item_index'][idx]
        title = self.data['title'][idx] if 'title' in self.data.columns else f"Book_{item_id}"
        author = self.data['author'][idx] if 'author' in self.data.columns else "Unknown Author"
        label = self.labels[idx]

        # Create prompt for P5 (BookCrossing specific)
        prompt = f"Will user_{user_id} likely to read book '{title}' by {author}?"
        target = "yes" if label == 1 else "no"

        # Tokenize inputs
        inputs = self.tokenizer(
            prompt,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )

        # Tokenize targets
        targets = self.tokenizer(
            target,
            max_length=10,  # Short target
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )

        # Extract user features for this sample
        user_features = {
            'user_index': self.user_features['user_index'][idx],
            'age': self.user_features['age'][idx],
            'city_index': self.user_features['city_index'][idx],
            'state_index': self.user_features['state_index'][idx],
            'country_index': self.user_features['country_index'][idx]
        }

        # Extract item features for this sample
        item_features = {
            'item_index': self.item_features['item_index'][idx],
            'author_index': self.item_features['author_index'][idx],
            'publisher_index': self.item_features['publisher_index'][idx],
            'year': self.item_features['year'][idx]
        }

        return {
            'input_ids': inputs.input_ids.squeeze(),
            'attention_mask': inputs.attention_mask.squeeze(),
            'labels': targets.input_ids.squeeze(),
            'user_features': user_features,
            'item_features': item_features,
            'raw_label': torch.tensor(label, dtype=torch.long)
        }


class FeatureEmbedder(nn.Module):
    """Feature embedder for user and item features"""
    def __init__(self, feature_dims, embedding_dims, device):
        super().__init__()
        self.device = device
        self.embeddings = nn.ModuleDict()

        for feature_name, vocab_size in feature_dims.items():
            embed_dim = embedding_dims.get(feature_name, 16)
            self.embeddings[feature_name] = nn.Embedding(vocab_size, embed_dim)

    def forward(self, features):
        embedded_features = []
        for feature_name, feature_values in features.items():
            if feature_name in self.embeddings:
                # Handle potential out-of-bounds indices
                feature_values = torch.clamp(feature_values, 0, self.embeddings[feature_name].num_embeddings - 1)
                embedded = self.embeddings[feature_name](feature_values)
                embedded_features.append(embedded)

        if embedded_features:
            return torch.cat(embedded_features, dim=1)
        else:
            # Return zero tensor if no features
            return torch.zeros(features[list(features.keys())[0]].size(0), 16).to(self.device)


class P5ForBookCrossingCTR(nn.Module):
    """P5 model adapted for BookCrossing CTR prediction"""
    def __init__(self, p5_model, user_embedder, item_embedder, device):
        super().__init__()
        self.p5_model = p5_model
        self.user_embedder = user_embedder
        self.item_embedder = item_embedder
        self.device = device

        # Add a classification head
        encoder_dim = p5_model.config.d_model
        user_embed_dim = 80  # 5 features * 16 dim each
        item_embed_dim = 64  # 4 features * 16 dim each
        combined_dim = encoder_dim + user_embed_dim + item_embed_dim

        self.classifier = nn.Sequential(
            nn.Linear(combined_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 2)
        )

    def forward(self, input_ids, attention_mask, user_features, item_features, labels=None):
        # Get user and item embeddings
        user_embeddings = self.user_embedder(user_features)
        item_embeddings = self.item_embedder(item_features)

        # Create encoder outputs directly
        encoder_outputs = self.p5_model.encoder(
            input_ids=input_ids,
            attention_mask=attention_mask,
            return_dict=True
        )

        # Get the last hidden state from encoder (use [CLS] token equivalent)
        last_hidden_state = encoder_outputs.last_hidden_state[:, 0, :]

        # Concatenate with user and item embeddings
        combined_features = torch.cat([last_hidden_state, user_embeddings, item_embeddings], dim=1)

        # Classification
        logits = self.classifier(combined_features)

        loss = None
        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(logits.view(-1, 2), labels.view(-1))

        return {
            'loss': loss,
            'logits': logits
        }


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train P5 on BookCrossing dataset')

    parser.add_argument("--data_dir", type=str, default="/data/datasets/processed_datasets/bookcrossing",
                        help="Directory containing the processed data")
    parser.add_argument("--output_dir", type=str, default="output_bookcrossing_p5",
                        help="Directory to save model and results")
    parser.add_argument("--max_length", type=int, default=128,
                        help="Maximum sequence length")
    parser.add_argument("--batch_size", type=int, default=16,
                        help="Batch size for training")
    parser.add_argument("--eval_batch_size", type=int, default=32,
                        help="Batch size for evaluation")
    parser.add_argument("--learning_rate", type=float, default=1e-4,
                        help="Learning rate")
    parser.add_argument("--num_epochs", type=int, default=10,
                        help="Number of training epochs")
    parser.add_argument("--weight_decay", type=float, default=0.01,
                        help="Weight decay for regularization")
    parser.add_argument("--warmup_steps", type=int, default=500,
                        help="Number of warmup steps")
    parser.add_argument("--patience", type=int, default=3,
                        help="Early stopping patience")
    parser.add_argument("--seed", type=int, default=2023,
                        help="Random seed")

    return parser.parse_args()


def set_seed(seed):
    """Set random seed for reproducibility."""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


def train_epoch(model, dataloader, optimizer, device):
    """Train for one epoch"""
    model.train()
    total_loss = 0.0

    with tqdm(dataloader, desc="Training") as pbar:
        for batch in pbar:
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['raw_label'].to(device)

            # Process user and item features
            user_features = {k: torch.tensor(v, dtype=torch.long).to(device)
                            for k, v in batch['user_features'].items()}

            item_features = {k: torch.tensor(v, dtype=torch.long).to(device)
                            for k, v in batch['item_features'].items()}

            # Forward pass
            outputs = model(input_ids, attention_mask, user_features, item_features, labels)
            loss = outputs['loss']

            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            pbar.set_postfix({'loss': loss.item()})

    return total_loss / len(dataloader)


def evaluate(model, dataloader, device):
    """Evaluate model on validation or test set"""
    model.eval()
    total_loss = 0.0
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['raw_label'].to(device)

            # Process user and item features
            user_features = {k: torch.tensor(v, dtype=torch.long).to(device)
                            for k, v in batch['user_features'].items()}

            item_features = {k: torch.tensor(v, dtype=torch.long).to(device)
                            for k, v in batch['item_features'].items()}

            # Forward pass
            outputs = model(input_ids, attention_mask, user_features, item_features, labels)
            loss = outputs['loss']
            logits = outputs['logits']

            total_loss += loss.item()

            # Get predictions
            preds = torch.softmax(logits, dim=1)[:, 1].cpu().numpy()
            all_preds.extend(preds)
            all_labels.extend(batch['raw_label'].numpy())

    # Calculate metrics
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)

    # Clip predictions to avoid numerical issues
    all_preds = np.clip(all_preds, 1e-6, 1 - 1e-6)

    auc = roc_auc_score(all_labels, all_preds)
    logloss = log_loss(all_labels, all_preds)
    binary_preds = (all_preds >= 0.5).astype(int)
    accuracy = accuracy_score(all_labels, binary_preds)
    precision = precision_score(all_labels, binary_preds, zero_division=0)
    recall = recall_score(all_labels, binary_preds, zero_division=0)
    f1 = f1_score(all_labels, binary_preds, zero_division=0)

    return {
        'loss': total_loss / len(dataloader),
        'auc': auc,
        'logloss': logloss,
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1
    }


def main():
    """Main training function."""
    # Parse arguments
    args = parse_args()

    # Set random seed
    set_seed(args.seed)

    # Initialize wandb
    wandb.init(
        project="ctr-prediction-BookCrossing-baselines",
        name=f"P5_lr{args.learning_rate}_bs{args.batch_size}_maxlen{args.max_length}",
        config={
            "model": "P5",
            "dataset": "BookCrossing",
            "batch_size": args.batch_size,
            "learning_rate": args.learning_rate,
            "weight_decay": args.weight_decay,
            "num_epochs": args.num_epochs,
            "max_length": args.max_length,
            "warmup_steps": args.warmup_steps,
            "seed": args.seed
        }
    )

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # Load tokenizer
    tokenizer = T5Tokenizer.from_pretrained('/data/p5_model')

    # Load data
    train_file = os.path.join(args.data_dir, 'train.csv')
    val_file = os.path.join(args.data_dir, 'val.csv')
    test_file = os.path.join(args.data_dir, 'test.csv')

    # Create datasets
    logger.info("Loading datasets...")
    train_dataset = BookCrossingP5Dataset(train_file, tokenizer, max_length=args.max_length, mode='train')
    val_dataset = BookCrossingP5Dataset(val_file, tokenizer, max_length=args.max_length, mode='val')
    test_dataset = BookCrossingP5Dataset(test_file, tokenizer, max_length=args.max_length, mode='test')

    logger.info(f"Train samples: {len(train_dataset)}")
    logger.info(f"Val samples: {len(val_dataset)}")
    logger.info(f"Test samples: {len(test_dataset)}")

    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.eval_batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=args.eval_batch_size, shuffle=False)

    # Load P5 model
    logger.info("Loading P5 model...")
    config = T5Config.from_pretrained('/data/p5_model')
    p5_model = P5(config)
    p5_model.load_state_dict(torch.load('/data/p5_model/pytorch_model.bin', map_location=device))

    # Initialize feature embedders
    user_feature_dims = {
        'user_index': train_dataset.data['user_index'].max() + 1,
        'age': train_dataset.data['age'].max() + 1,
        'city_index': train_dataset.data['city_index'].max() + 1,
        'state_index': train_dataset.data['state_index'].max() + 1,
        'country_index': train_dataset.data['country_index'].max() + 1
    }

    item_feature_dims = {
        'item_index': train_dataset.data['item_index'].max() + 1,
        'author_index': train_dataset.data['author_index'].max() + 1,
        'publisher_index': train_dataset.data['publisher_index'].max() + 1,
        'year': train_dataset.data['year'].max() + 1
    }

    embedding_dims = {
        'user_index': 16,
        'item_index': 16,
        'age': 16,
        'city_index': 16,
        'state_index': 16,
        'country_index': 16,
        'author_index': 16,
        'publisher_index': 16,
        'year': 16
    }

    user_embedder = FeatureEmbedder(user_feature_dims, embedding_dims, device)
    item_embedder = FeatureEmbedder(item_feature_dims, embedding_dims, device)

    # Initialize P5 for CTR model
    model = P5ForBookCrossingCTR(p5_model, user_embedder, item_embedder, device)
    model.to(device)

    logger.info(f"Model created with {sum(p.numel() for p in model.parameters())} parameters")

    # Initialize optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)

    # Training loop
    logger.info("Starting training...")
    best_auc = 0.0
    patience_counter = 0

    for epoch in range(args.num_epochs):
        # Train
        start_time = time.time()
        train_loss = train_epoch(model, train_loader, optimizer, device)
        train_time = time.time() - start_time

        # Validate
        val_metrics = evaluate(model, val_loader, device)
        test_metrics = evaluate(model, test_loader, device)

        # Log results
        logger.info(f"Epoch {epoch+1}/{args.num_epochs}")
        logger.info(f"Train Loss: {train_loss:.4f}")
        logger.info(f"Val Loss: {val_metrics['loss']:.4f}")
        logger.info(f"Val AUC: {val_metrics['auc']:.4f}")
        logger.info(f"Val LogLoss: {val_metrics['logloss']:.4f}")
        logger.info(f"Test AUC: {test_metrics['auc']:.4f}")
        logger.info(f"Test LogLoss: {test_metrics['logloss']:.4f}")
        logger.info(f"Time: {train_time:.2f}s")

        # Log to wandb
        wandb.log({
            "epoch": epoch + 1,
            "train_loss": train_loss,
            "val_loss": val_metrics['loss'],
            "val_auc": val_metrics['auc'],
            "val_logloss": val_metrics['logloss'],
            "val_accuracy": val_metrics['accuracy'],
            "test_auc": test_metrics['auc'],
            "test_logloss": test_metrics['logloss'],
            "test_accuracy": test_metrics['accuracy'],
            "train_time": train_time
        })

        # Save best model
        if val_metrics['auc'] > best_auc:
            best_auc = val_metrics['auc']
            patience_counter = 0

            # Save model
            model_path = os.path.join(args.output_dir, 'best_p5_model.pt')
            torch.save({
                'model_state_dict': model.state_dict(),
                'user_feature_dims': user_feature_dims,
                'item_feature_dims': item_feature_dims,
                'embedding_dims': embedding_dims,
                'config': config
            }, model_path)
            logger.info(f"New best model saved with AUC: {val_metrics['auc']:.4f}")
        else:
            patience_counter += 1
            if patience_counter >= args.patience:
                logger.info(f"Early stopping at epoch {epoch+1}")
                break

    # Final evaluation
    model.load_state_dict(torch.load(os.path.join(args.output_dir, 'best_p5_model.pt'))['model_state_dict'])
    final_test_metrics = evaluate(model, test_loader, device)

    logger.info(f"Training completed. Best validation AUC: {best_auc:.4f}")
    logger.info(f"Final test metrics - AUC: {final_test_metrics['auc']:.4f}, "
               f"LogLoss: {final_test_metrics['logloss']:.4f}, "
               f"Accuracy: {final_test_metrics['accuracy']:.4f}")

    # Save final results
    results = {
        'model': 'P5',
        'best_validation_auc': float(best_auc),
        'final_test_auc': float(final_test_metrics['auc']),
        'final_test_logloss': float(final_test_metrics['logloss']),
        'final_test_accuracy': float(final_test_metrics['accuracy']),
        'final_test_precision': float(final_test_metrics['precision']),
        'final_test_recall': float(final_test_metrics['recall']),
        'final_test_f1': float(final_test_metrics['f1'])
    }

    # Log final results to wandb
    wandb.log({
        "best_validation_auc": best_auc,
        "final_test_auc": final_test_metrics['auc'],
        "final_test_logloss": final_test_metrics['logloss'],
        "final_test_accuracy": final_test_metrics['accuracy']
    })

    # Log summary metrics
    wandb.run.summary["best_validation_auc"] = best_auc
    wandb.run.summary["final_test_auc"] = final_test_metrics['auc']
    wandb.run.summary["final_test_logloss"] = final_test_metrics['logloss']
    wandb.run.summary["final_test_accuracy"] = final_test_metrics['accuracy']

    results_path = os.path.join(args.output_dir, 'results.json')
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"Results saved to {results_path}")

    # Finish wandb run
    wandb.finish()


if __name__ == "__main__":
    main()
