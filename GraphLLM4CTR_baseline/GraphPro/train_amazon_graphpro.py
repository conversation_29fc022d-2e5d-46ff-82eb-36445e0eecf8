#!/usr/bin/env python3
"""
Training script for GraphPro on Amazon dataset.
This script handles data preparation, model training, and CTR evaluation.
"""
import os
import sys
import json
import argparse
import logging
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score
from tqdm import tqdm
import wandb

# Add GraphPro utils to path
sys.path.append('/root/code/GraphLLM4CTR_baseline/GraphPro')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AmazonCTRDataset(Dataset):
    """Dataset for CTR prediction using Amazon data"""
    def __init__(self, csv_file, user_embeddings=None, item_embeddings=None, emb_size=64):
        """
        Args:
            csv_file: Path to the CSV file with user-item interactions
            user_embeddings: Pre-trained user embeddings (optional)
            item_embeddings: Pre-trained item embeddings (optional)
            emb_size: Embedding size if creating random embeddings
        """
        self.data = pd.read_csv(csv_file)
        self.emb_size = emb_size

        # Get unique users and items
        self.num_users = self.data['user_id'].max() + 1
        self.num_items = self.data['item_id'].max() + 1

        # Use provided embeddings or create random ones
        if user_embeddings is not None:
            self.user_embeddings = user_embeddings
        else:
            np.random.seed(42)
            self.user_embeddings = np.random.normal(0, 0.1, (self.num_users, emb_size))

        if item_embeddings is not None:
            self.item_embeddings = item_embeddings
        else:
            np.random.seed(43)
            self.item_embeddings = np.random.normal(0, 0.1, (self.num_items, emb_size))

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        user_id = row['user_id']
        item_id = row['item_id']
        label = row['label']

        # Get embeddings (ensure indices are within bounds)
        user_idx = min(user_id, len(self.user_embeddings)-1)
        item_idx = min(item_id, len(self.item_embeddings)-1)

        user_emb = self.user_embeddings[user_idx]
        item_emb = self.item_embeddings[item_idx]

        return {
            'user_emb': torch.tensor(user_emb, dtype=torch.float32),
            'item_emb': torch.tensor(item_emb, dtype=torch.float32),
            'label': torch.tensor(label, dtype=torch.float32)
        }


class GraphProCTRPredictor(nn.Module):
    """CTR prediction model using GraphPro embeddings"""
    def __init__(self, input_dim, hidden_dim=128, dropout=0.2):
        super(GraphProCTRPredictor, self).__init__()
        self.mlp = nn.Sequential(
            nn.Linear(input_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )

    def forward(self, user_emb, item_emb):
        # Concatenate user and item embeddings
        x = torch.cat([user_emb, item_emb], dim=1)
        return self.mlp(x).squeeze()


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train GraphPro on BookCrossing dataset')

    parser.add_argument('--data_dir', type=str, default='/data/datasets/processed_datasets/amazon',
                        help='Directory containing the processed data')
    parser.add_argument('--output_dir', type=str, default='output_amazon_graphpro',
                        help='Directory to save model and results')
    parser.add_argument('--batch_size', type=int, default=256,
                        help='Batch size for training')
    parser.add_argument('--eval_batch_size', type=int, default=512,
                        help='Batch size for evaluation')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--num_epochs', type=int, default=30,
                        help='Number of training epochs')
    parser.add_argument('--emb_size', type=int, default=64,
                        help='Embedding size')
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='Hidden layer size')
    parser.add_argument('--dropout', type=float, default=0.2,
                        help='Dropout rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4,
                        help='Weight decay for regularization')
    parser.add_argument('--patience', type=int, default=5,
                        help='Early stopping patience')
    parser.add_argument('--seed', type=int, default=2023,
                        help='Random seed')

    return parser.parse_args()


def set_seed(seed):
    """Set random seed for reproducibility."""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


def train_epoch(model, dataloader, optimizer, criterion, device):
    """Train for one epoch"""
    model.train()
    total_loss = 0

    with tqdm(dataloader, desc="Training") as pbar:
        for batch in pbar:
            user_emb = batch['user_emb'].to(device)
            item_emb = batch['item_emb'].to(device)
            labels = batch['label'].to(device)

            optimizer.zero_grad()
            outputs = model(user_emb, item_emb)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            pbar.set_postfix({'loss': loss.item()})

    return total_loss / len(dataloader)


def evaluate(model, dataloader, device):
    """Evaluate model on validation or test set"""
    model.eval()
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            user_emb = batch['user_emb'].to(device)
            item_emb = batch['item_emb'].to(device)
            labels = batch['label'].to(device)

            outputs = model(user_emb, item_emb)
            preds = torch.sigmoid(outputs)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    # Calculate metrics
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)

    # Clip predictions to avoid numerical issues
    all_preds = np.clip(all_preds, 1e-6, 1 - 1e-6)

    auc = roc_auc_score(all_labels, all_preds)
    logloss = log_loss(all_labels, all_preds)
    accuracy = accuracy_score(all_labels, (all_preds >= 0.5).astype(int))

    return {
        'auc': auc,
        'logloss': logloss,
        'accuracy': accuracy
    }


def main():
    """Main training function."""
    # Parse arguments
    args = parse_args()

    # Set random seed
    set_seed(args.seed)

    # Initialize wandb
    wandb.init(
        project="ctr-prediction-Amazon-baselines",
        name=f"GraphPro_lr{args.lr}_bs{args.batch_size}_emb{args.emb_size}_hidden{args.hidden_dim}",
        config={
            "model": "GraphPro",
            "dataset": "Amazon",
            "batch_size": args.batch_size,
            "learning_rate": args.lr,
            "weight_decay": args.weight_decay,
            "num_epochs": args.num_epochs,
            "emb_size": args.emb_size,
            "hidden_dim": args.hidden_dim,
            "dropout": args.dropout,
            "seed": args.seed
        }
    )

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Load data
    train_csv = os.path.join(args.data_dir, 'train_new.csv')
    val_csv = os.path.join(args.data_dir, 'val_new.csv')
    test_csv = os.path.join(args.data_dir, 'test_new.csv')

    logger.info("Loading datasets...")
    train_dataset = AmazonCTRDataset(train_csv, emb_size=args.emb_size)
    val_dataset = AmazonCTRDataset(val_csv,
                                  train_dataset.user_embeddings,
                                  train_dataset.item_embeddings,
                                  args.emb_size)
    test_dataset = AmazonCTRDataset(test_csv,
                                   train_dataset.user_embeddings,
                                   train_dataset.item_embeddings,
                                   args.emb_size)

    logger.info(f"Train samples: {len(train_dataset)}")
    logger.info(f"Val samples: {len(val_dataset)}")
    logger.info(f"Test samples: {len(test_dataset)}")
    logger.info(f"Users: {train_dataset.num_users}, Items: {train_dataset.num_items}")

    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.eval_batch_size)
    test_loader = DataLoader(test_dataset, batch_size=args.eval_batch_size)

    # Initialize model
    model = GraphProCTRPredictor(args.emb_size, args.hidden_dim, args.dropout).to(device)
    logger.info(f"Model created with {sum(p.numel() for p in model.parameters())} parameters")

    # Define optimizer and loss
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    criterion = nn.BCEWithLogitsLoss()

    # Training loop
    logger.info("Starting training...")
    best_val_auc = 0
    patience_counter = 0

    for epoch in range(args.num_epochs):
        # Train
        start_time = time.time()
        train_loss = train_epoch(model, train_loader, optimizer, criterion, device)
        train_time = time.time() - start_time

        # Validate
        val_metrics = evaluate(model, val_loader, device)
        test_metrics = evaluate(model, test_loader, device)

        # Log results
        logger.info(f"Epoch {epoch+1}/{args.num_epochs} - "
                   f"Train Loss: {train_loss:.4f} - "
                   f"Val AUC: {val_metrics['auc']:.4f} - "
                   f"Val LogLoss: {val_metrics['logloss']:.4f} - "
                   f"Test AUC: {test_metrics['auc']:.4f} - "
                   f"Test LogLoss: {test_metrics['logloss']:.4f} - "
                   f"Time: {train_time:.2f}s")

        # Log to wandb
        wandb.log({
            "epoch": epoch + 1,
            "train_loss": train_loss,
            "val_auc": val_metrics['auc'],
            "val_logloss": val_metrics['logloss'],
            "val_accuracy": val_metrics['accuracy'],
            "test_auc": test_metrics['auc'],
            "test_logloss": test_metrics['logloss'],
            "test_accuracy": test_metrics['accuracy'],
            "train_time": train_time
        })

        # Early stopping and model saving
        if val_metrics['auc'] > best_val_auc:
            best_val_auc = val_metrics['auc']
            patience_counter = 0

            # Save best model
            model_path = os.path.join(args.output_dir, 'best_model.pth')
            torch.save(model.state_dict(), model_path)
            logger.info(f"New best model saved with validation AUC: {best_val_auc:.4f}")
        else:
            patience_counter += 1
            if patience_counter >= args.patience:
                logger.info(f"Early stopping at epoch {epoch+1}")
                break

    # Load best model for final evaluation
    model.load_state_dict(torch.load(os.path.join(args.output_dir, 'best_model.pth')))
    final_test_metrics = evaluate(model, test_loader, device)

    logger.info(f"Training completed. Best validation AUC: {best_val_auc:.4f}")
    logger.info(f"Final test metrics - AUC: {final_test_metrics['auc']:.4f}, "
               f"LogLoss: {final_test_metrics['logloss']:.4f}, "
               f"Accuracy: {final_test_metrics['accuracy']:.4f}")

    # Save final results
    results = {
        'model': 'GraphPro',
        'best_validation_auc': float(best_val_auc),
        'final_test_auc': float(final_test_metrics['auc']),
        'final_test_logloss': float(final_test_metrics['logloss']),
        'final_test_accuracy': float(final_test_metrics['accuracy'])
    }

    # Log final results to wandb
    wandb.log({
        "best_validation_auc": best_val_auc,
        "final_test_auc": final_test_metrics['auc'],
        "final_test_logloss": final_test_metrics['logloss'],
        "final_test_accuracy": final_test_metrics['accuracy']
    })

    # Log summary metrics
    wandb.run.summary["best_validation_auc"] = best_val_auc
    wandb.run.summary["final_test_auc"] = final_test_metrics['auc']
    wandb.run.summary["final_test_logloss"] = final_test_metrics['logloss']
    wandb.run.summary["final_test_accuracy"] = final_test_metrics['accuracy']

    results_path = os.path.join(args.output_dir, 'results.json')
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"Results saved to {results_path}")

    # Finish wandb run
    wandb.finish()


if __name__ == "__main__":
    main()
