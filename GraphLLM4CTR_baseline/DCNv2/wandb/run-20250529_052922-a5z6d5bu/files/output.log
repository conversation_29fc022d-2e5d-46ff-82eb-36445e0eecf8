2025-05-29 05:29:24,238 - __main__ - INFO - Using device: cuda
2025-05-29 05:29:24,385 - __main__ - INFO - Number of fields: 7
2025-05-29 05:29:24,385 - __main__ - INFO - Input size: 251744
2025-05-29 05:29:24,385 - __main__ - INFO - Field names: ['user_id', 'item_id', 'brand_index', 'price_range_index', 'category_1', 'category_2', 'category_3']
2025-05-29 05:29:24,587 - __main__ - INFO - Model created with 4097089 parameters
2025-05-29 05:29:25,105 - __main__ - INFO - Starting training...
Training: 100%|██████████| 2124/2124 [00:09<00:00, 215.63it/s, loss=0.59] 
2025-05-29 05:29:37,767 - __main__ - INFO - Epoch 1/5 - Train Loss: 0.6216 - Valid AUC: 0.5077 - Valid LogLoss: 0.6171 - Test AUC: 0.5061 - Test LogLoss: 0.6186 - Time: 9.85s
2025-05-29 05:29:37,800 - __main__ - INFO - New best model saved with validation AUC: 0.5077
Training: 100%|██████████| 2124/2124 [00:09<00:00, 223.45it/s, loss=0.54] 
2025-05-29 05:29:50,085 - __main__ - INFO - Epoch 2/5 - Train Loss: 0.6232 - Valid AUC: 0.4954 - Valid LogLoss: 0.6187 - Test AUC: 0.4922 - Test LogLoss: 0.6201 - Time: 9.51s
Training: 100%|██████████| 2124/2124 [00:09<00:00, 225.09it/s, loss=0.669]
2025-05-29 05:30:02,244 - __main__ - INFO - Epoch 3/5 - Train Loss: 0.6238 - Valid AUC: 0.5024 - Valid LogLoss: 0.6186 - Test AUC: 0.5018 - Test LogLoss: 0.6199 - Time: 9.44s
Training: 100%|██████████| 2124/2124 [00:09<00:00, 225.90it/s, loss=0.676]
2025-05-29 05:30:14,307 - __main__ - INFO - Epoch 4/5 - Train Loss: 0.6238 - Valid AUC: 0.5000 - Valid LogLoss: 0.6186 - Test AUC: 0.5000 - Test LogLoss: 0.6199 - Time: 9.40s
Training: 100%|██████████| 2124/2124 [00:09<00:00, 225.12it/s, loss=0.591]
2025-05-29 05:30:26,399 - __main__ - INFO - Epoch 5/5 - Train Loss: 0.6238 - Valid AUC: 0.5000 - Valid LogLoss: 0.6186 - Test AUC: 0.5000 - Test LogLoss: 0.6200 - Time: 9.44s
train_amazon.py:236: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  model.load_state_dict(torch.load(os.path.join(training_config.output_dir, 'best_model.pth')))
2025-05-29 05:30:27,933 - __main__ - INFO - Training completed. Best validation AUC: 0.5077 at epoch 1
2025-05-29 05:30:27,933 - __main__ - INFO - Final test metrics - AUC: 0.5061, LogLoss: 0.6186, Accuracy: 0.6927
2025-05-29 05:30:27,935 - __main__ - INFO - Results saved to output_amazon_dcnv2/results.json
