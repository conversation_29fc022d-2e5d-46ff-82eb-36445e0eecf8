2025-05-29 05:42:32,277 - __main__ - INFO - Using device: cuda
2025-05-29 05:42:32,422 - __main__ - INFO - Number of fields: 7
2025-05-29 05:42:32,423 - __main__ - INFO - Input size: 251744
2025-05-29 05:42:32,423 - __main__ - INFO - Field names: ['user_id', 'item_id', 'brand_index', 'price_range_index', 'category_1', 'category_2', 'category_3']
2025-05-29 05:42:32,677 - __main__ - INFO - Model created with 4097089 parameters
2025-05-29 05:42:33,188 - __main__ - INFO - Starting training...
Training: 100%|██████████| 2124/2124 [00:09<00:00, 219.05it/s, loss=0.59] 
2025-05-29 05:42:45,683 - __main__ - INFO - Epoch 1/5 - Train Loss: 0.6216 - Valid AUC: 0.5077 - Valid LogLoss: 0.6171 - Test AUC: 0.5061 - Test LogLoss: 0.6186 - Time: 9.70s
2025-05-29 05:42:45,714 - __main__ - INFO - New best model saved with validation AUC: 0.5077
Training: 100%|██████████| 2124/2124 [00:09<00:00, 220.53it/s, loss=0.54] 
2025-05-29 05:42:58,221 - __main__ - INFO - Epoch 2/5 - Train Loss: 0.6232 - Valid AUC: 0.4954 - Valid LogLoss: 0.6187 - Test AUC: 0.4922 - Test LogLoss: 0.6201 - Time: 9.63s
Training: 100%|██████████| 2124/2124 [00:09<00:00, 223.42it/s, loss=0.669]
2025-05-29 05:43:10,520 - __main__ - INFO - Epoch 3/5 - Train Loss: 0.6238 - Valid AUC: 0.5024 - Valid LogLoss: 0.6186 - Test AUC: 0.5018 - Test LogLoss: 0.6199 - Time: 9.51s
Training: 100%|██████████| 2124/2124 [00:09<00:00, 225.18it/s, loss=0.676]
2025-05-29 05:43:22,497 - __main__ - INFO - Epoch 4/5 - Train Loss: 0.6238 - Valid AUC: 0.5000 - Valid LogLoss: 0.6186 - Test AUC: 0.5000 - Test LogLoss: 0.6199 - Time: 9.43s
Training: 100%|██████████| 2124/2124 [00:09<00:00, 225.77it/s, loss=0.591]
