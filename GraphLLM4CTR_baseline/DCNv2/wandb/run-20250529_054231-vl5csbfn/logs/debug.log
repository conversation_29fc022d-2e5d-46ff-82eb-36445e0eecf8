2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_setup.py:_flush():68] Current SDK version is 0.19.10
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_setup.py:_flush():68] Configure stats pid to 95410
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_setup.py:_flush():68] Loading settings from /root/.config/wandb/settings
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_setup.py:_flush():68] Loading settings from /root/code/GraphLLM4CTR_baseline/DCNv2/wandb/settings
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/GraphLLM4CTR_baseline/DCNv2/wandb/run-20250529_054231-vl5csbfn/logs/debug.log
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/GraphLLM4CTR_baseline/DCNv2/wandb/run-20250529_054231-vl5csbfn/logs/debug-internal.log
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_init.py:init():852] calling init triggers
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model': 'DCNv2', 'dataset': 'Amazon', 'batch_size': 512, 'learning_rate': 0.001, 'weight_decay': 0.05, 'num_epochs': 5, 'embed_size': 16, 'hidden_size': 128, 'num_hidden_layers': 2, 'num_cross_layers': 3, 'dropout': 0.2, 'seed': 2023, '_wandb': {}}
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_init.py:init():893] starting backend
2025-05-29 05:42:31,115 INFO    MainThread:95410 [wandb_init.py:init():897] sending inform_init request
2025-05-29 05:42:31,137 INFO    MainThread:95410 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-29 05:42:31,137 INFO    MainThread:95410 [wandb_init.py:init():907] backend started and connected
2025-05-29 05:42:31,139 INFO    MainThread:95410 [wandb_init.py:init():1002] updated telemetry
2025-05-29 05:42:31,155 INFO    MainThread:95410 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
2025-05-29 05:42:32,153 INFO    MainThread:95410 [wandb_init.py:init():1101] starting run threads in backend
2025-05-29 05:42:32,274 INFO    MainThread:95410 [wandb_run.py:_console_start():2566] atexit reg
2025-05-29 05:42:32,274 INFO    MainThread:95410 [wandb_run.py:_redirect():2414] redirect: wrap_raw
2025-05-29 05:42:32,275 INFO    MainThread:95410 [wandb_run.py:_redirect():2483] Wrapping output streams.
2025-05-29 05:42:32,275 INFO    MainThread:95410 [wandb_run.py:_redirect():2506] Redirects installed.
2025-05-29 05:42:32,277 INFO    MainThread:95410 [wandb_init.py:init():1147] run started, returning control to user process
