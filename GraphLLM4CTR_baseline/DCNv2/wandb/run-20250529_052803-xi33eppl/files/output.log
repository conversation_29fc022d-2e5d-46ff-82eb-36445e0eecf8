2025-05-29 05:28:05,362 - __main__ - INFO - Using device: cuda
2025-05-29 05:28:05,507 - __main__ - INFO - Number of fields: 7
2025-05-29 05:28:05,507 - __main__ - INFO - Input size: 251744
2025-05-29 05:28:05,507 - __main__ - INFO - Field names: ['user_id', 'item_id', 'brand_index', 'price_range_index', 'category_1', 'category_2', 'category_3']
2025-05-29 05:28:05,793 - __main__ - INFO - Model created with 4097089 parameters
2025-05-29 05:28:06,292 - __main__ - INFO - Starting training...
Training: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2124/2124 [00:09<00:00, 217.54it/s, loss=0.59]
2025-05-29 05:28:18,791 - __main__ - INFO - Epoch 1/2 - Train Loss: 0.6216 - Valid AUC: 0.5077 - Valid Log<PERSON>oss: 0.6171 - Test AUC: 0.5061 - Test LogLoss: 0.6186 - Time: 9.76s
2025-05-29 05:28:18,816 - __main__ - INFO - New best model saved with validation AUC: 0.5077
Training: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2124/2124 [00:09<00:00, 222.10it/s, loss=0.54]
2025-05-29 05:28:31,131 - __main__ - INFO - Epoch 2/2 - Train Loss: 0.6232 - Valid AUC: 0.4954 - Valid LogLoss: 0.6187 - Test AUC: 0.4922 - Test LogLoss: 0.6201 - Time: 9.56s
train_amazon.py:236: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  model.load_state_dict(torch.load(os.path.join(training_config.output_dir, 'best_model.pth')))
2025-05-29 05:28:32,703 - __main__ - INFO - Training completed. Best validation AUC: 0.5077 at epoch 1
2025-05-29 05:28:32,703 - __main__ - INFO - Final test metrics - AUC: 0.5061, LogLoss: 0.6186, Accuracy: 0.6927
2025-05-29 05:28:32,705 - __main__ - INFO - Results saved to output_amazon_dcnv2/results.json
