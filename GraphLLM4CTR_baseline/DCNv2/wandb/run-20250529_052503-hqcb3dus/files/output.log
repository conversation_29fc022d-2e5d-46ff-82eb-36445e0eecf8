2025-05-29 05:25:04,525 - __main__ - INFO - Using device: cuda
Traceback (most recent call last):
  File "train_amazon.py", line 280, in <module>
    main()
  File "train_amazon.py", line 156, in main
    train_loader, valid_loader, test_loader = get_data_loaders(
  File "/root/code/GraphLLM4CTR_baseline/DCNv2/utils/data_processor.py", line 184, in get_data_loaders
    with h5py.File(h5_data_path, 'r') as f:
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/h5py/_hl/files.py", line 406, in __init__
    fid = make_fid(name, mode, userblock_size,
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/h5py/_hl/files.py", line 173, in make_fid
    fid = h5f.open(name, flags, fapl=fapl)
  File "h5py/_objects.pyx", line 54, in h5py._objects.with_phil.wrapper
  File "h5py/_objects.pyx", line 55, in h5py._objects.with_phil.wrapper
  File "h5py/h5f.pyx", line 88, in h5py.h5f.open
OSError: Unable to open file (unable to open file: name = '/data/datasets/processed_datasets/amazon/ctr.h5', errno = 2, error message = 'No such file or directory', flags = 0, o_flags = 0)
Traceback (most recent call last):
  File "train_amazon.py", line 280, in <module>
    main()
  File "train_amazon.py", line 156, in main
    train_loader, valid_loader, test_loader = get_data_loaders(
  File "/root/code/GraphLLM4CTR_baseline/DCNv2/utils/data_processor.py", line 184, in get_data_loaders
    with h5py.File(h5_data_path, 'r') as f:
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/h5py/_hl/files.py", line 406, in __init__
    fid = make_fid(name, mode, userblock_size,
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/h5py/_hl/files.py", line 173, in make_fid
    fid = h5f.open(name, flags, fapl=fapl)
  File "h5py/_objects.pyx", line 54, in h5py._objects.with_phil.wrapper
  File "h5py/_objects.pyx", line 55, in h5py._objects.with_phil.wrapper
  File "h5py/h5f.pyx", line 88, in h5py.h5f.open
OSError: Unable to open file (unable to open file: name = '/data/datasets/processed_datasets/amazon/ctr.h5', errno = 2, error message = 'No such file or directory', flags = 0, o_flags = 0)
