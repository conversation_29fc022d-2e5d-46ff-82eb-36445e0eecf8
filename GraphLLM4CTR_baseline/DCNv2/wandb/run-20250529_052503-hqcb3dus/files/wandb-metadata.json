{"os": "Linux-5.4.0-216-generic-x86_64-with-glibc2.17", "python": "CPython 3.8.19", "startedAt": "2025-05-29T05:25:03.228130Z", "args": ["--num_epochs", "2"], "program": "train_amazon.py", "codePath": "DCNv2/train_amazon.py", "git": {"remote": "**************:gaoshan-code/GraphLLM4CTR_baseline.git", "commit": "94356e195b6f8ba50718806c3e1ba6c0fb57a79c"}, "email": "<EMAIL>", "root": "/root/code/GraphLLM4CTR_baseline/DCNv2", "host": "vmInstancekdtfa3ig", "executable": "/root/miniconda3/envs/py38/bin/python", "codePathLocal": "train_amazon.py", "cpu_count": 16, "cpu_count_logical": 16, "gpu": "NVIDIA GeForce RTX 4090", "gpu_count": 1, "disk": {"/": {"total": "51835101184", "used": "42208034816"}}, "memory": {"total": "120219561984"}, "cpu": {"count": 16, "countLogical": 16}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}], "cudaVersion": "12.6"}