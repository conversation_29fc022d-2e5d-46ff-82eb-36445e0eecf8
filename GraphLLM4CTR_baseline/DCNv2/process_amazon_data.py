"""
<PERSON><PERSON><PERSON> to process Amazon dataset for DCNv2.
"""
import os
import argparse
import pandas as pd
import numpy as np
import h5py
import json
from utils.data_processor import (
    load_csv_data, create_feature_map, convert_to_ids,
    save_meta_data, save_h5_data
)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Process Amazon dataset for DCNv2')

    parser.add_argument('--train_file', type=str, default='/data/datasets/processed_datasets/amazon/train_new.csv',
                        help='Path to the training CSV file')
    parser.add_argument('--valid_file', type=str, default='/data/datasets/processed_datasets/amazon/val_new.csv',
                        help='Path to the validation CSV file')
    parser.add_argument('--test_file', type=str, default='/data/datasets/processed_datasets/amazon/test_new.csv',
                        help='Path to the testing CSV file')
    parser.add_argument('--output_dir', type=str, default='/data/datasets/processed_datasets/amazon',
                        help='Directory to save processed data')
    parser.add_argument('--categorical_cols', type=str,
                        default='user_id,item_id,brand_index,price_range_index,category_1,category_2,category_3',
                        help='Comma-separated list of categorical column names')
    parser.add_argument('--label_col', type=str, default='label',
                        help='Name of the label column')

    return parser.parse_args()


def process_amazon_data_with_validation(train_file: str, valid_file: str, test_file: str,
                                       meta_file: str, h5_file: str,
                                       categorical_cols: list, label_col: str = 'label'):
    """
    Process Amazon data with validation set and save to files.

    Args:
        train_file: Path to training CSV file
        valid_file: Path to validation CSV file
        test_file: Path to testing CSV file
        meta_file: Path to save meta data JSON file
        h5_file: Path to save H5 data file
        categorical_cols: List of categorical column names
        label_col: Name of the label column
    """
    # Load data
    print("Loading data files...")
    train_df = load_csv_data(train_file)
    valid_df = load_csv_data(valid_file)
    test_df = load_csv_data(test_file)

    print(f"Train data shape: {train_df.shape}")
    print(f"Validation data shape: {valid_df.shape}")
    print(f"Test data shape: {test_df.shape}")

    # Print column information
    print(f"Available columns: {list(train_df.columns)}")
    print(f"Categorical columns to use: {categorical_cols}")

    # Handle missing values in categorical columns
    for col in categorical_cols:
        if col in train_df.columns:
            # Fill missing values with a default value
            train_df[col] = train_df[col].fillna(-1)
            valid_df[col] = valid_df[col].fillna(-1)
            test_df[col] = test_df[col].fillna(-1)

            # Convert to int if possible (for age, year, etc.)
            if col in ['age', 'year']:
                train_df[col] = pd.to_numeric(train_df[col], errors='coerce').fillna(-1).astype(int)
                valid_df[col] = pd.to_numeric(valid_df[col], errors='coerce').fillna(-1).astype(int)
                test_df[col] = pd.to_numeric(test_df[col], errors='coerce').fillna(-1).astype(int)

    # Create feature map from all data (train + valid + test)
    print("Creating feature map...")
    combined_df = pd.concat([train_df, valid_df, test_df], ignore_index=True)
    meta_data = create_feature_map(combined_df, categorical_cols)

    # Convert to IDs
    print("Converting to feature IDs...")
    train_ids = convert_to_ids(train_df, meta_data)
    valid_ids = convert_to_ids(valid_df, meta_data)
    test_ids = convert_to_ids(test_df, meta_data)

    # Get labels
    train_labels = train_df[label_col].values
    valid_labels = valid_df[label_col].values
    test_labels = test_df[label_col].values

    print(f"Feature dimensions: {train_ids.shape}")
    print(f"Number of features per field: {meta_data['feature_count']}")
    print(f"Total features: {sum(meta_data['feature_count'])}")

    # Save meta data
    print(f"Saving meta data to {meta_file}...")
    save_meta_data(meta_data, meta_file)

    # Save processed data to H5 file
    print(f"Saving processed data to {h5_file}...")
    with h5py.File(h5_file, 'w') as f:
        f.create_dataset('train data', data=train_ids)
        f.create_dataset('train label', data=train_labels)
        f.create_dataset('valid data', data=valid_ids)
        f.create_dataset('valid label', data=valid_labels)
        f.create_dataset('test data', data=test_ids)
        f.create_dataset('test label', data=test_labels)

    print(f"Data processing complete. Files saved to {meta_file} and {h5_file}")


def main():
    """Main function to process data."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Parse categorical columns
    categorical_cols = args.categorical_cols.split(',')

    # Output file paths
    meta_file = os.path.join(args.output_dir, 'ctr-meta.json')
    h5_file = os.path.join(args.output_dir, 'ctr.h5')

    # Process data
    print(f"Processing data from {args.train_file}, {args.valid_file}, and {args.test_file}...")
    process_amazon_data_with_validation(
        train_file=args.train_file,
        valid_file=args.valid_file,
        test_file=args.test_file,
        meta_file=meta_file,
        h5_file=h5_file,
        categorical_cols=categorical_cols,
        label_col=args.label_col
    )

    print("Data processing completed successfully!")
    print(f"Meta data saved to: {meta_file}")
    print(f"H5 data saved to: {h5_file}")
    print("\nYou can now run the training script with:")
    print(f"python train_bookcrossing.py")


if __name__ == "__main__":
    main()
