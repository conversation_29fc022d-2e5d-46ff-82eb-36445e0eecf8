"""
Training script for DCNv2 on Amazon dataset.
"""
import os
import json
import argparse
import logging
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import wandb

from config import get_configs
from models.dcnv2 import DCNv2
from utils.data_processor import get_data_loaders
from utils.metrics import evaluate_model

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train DCNv2 on Amazon dataset')

    parser.add_argument('--data_dir', type=str, default='/data/datasets/processed_datasets/amazon',
                        help='Directory containing the processed data')
    parser.add_argument('--output_dir', type=str, default='output_amazon_dcnv2',
                        help='Directory to save model and results')
    parser.add_argument('--batch_size', type=int, default=512,
                        help='Batch size for training')
    parser.add_argument('--eval_batch_size', type=int, default=512,
                        help='Batch size for evaluation')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--num_epochs', type=int, default=10,
                        help='Number of training epochs')
    parser.add_argument('--embed_size', type=int, default=16,
                        help='Embedding size')
    parser.add_argument('--hidden_size', type=int, default=128,
                        help='Hidden layer size')
    parser.add_argument('--num_hidden_layers', type=int, default=2,
                        help='Number of hidden layers')
    parser.add_argument('--num_cross_layers', type=int, default=3,
                        help='Number of cross layers')
    parser.add_argument('--dropout', type=float, default=0.2,
                        help='Dropout rate')
    parser.add_argument('--weight_decay', type=float, default=0.05,
                        help='Weight decay for regularization')
    parser.add_argument('--seed', type=int, default=2023,
                        help='Random seed')

    return parser.parse_args()


def set_seed(seed):
    """Set random seed for reproducibility."""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


def train_epoch(model, train_loader, optimizer, criterion, device):
    """Train model for one epoch."""
    model.train()
    epoch_loss = 0

    with tqdm(train_loader, desc="Training") as pbar:
        for batch in pbar:
            # Get data
            input_ids = batch['input_ids'].to(device)
            labels = batch['labels'].to(device)

            # Forward pass
            optimizer.zero_grad()
            outputs = model(input_ids)
            loss = criterion(outputs.squeeze(), labels)

            # Backward pass
            loss.backward()
            optimizer.step()

            # Update metrics
            epoch_loss += loss.item()
            pbar.set_postfix({'loss': loss.item()})

    return epoch_loss / len(train_loader)


def main():
    """Main training function."""
    # Parse arguments
    args = parse_args()

    # Set random seed
    set_seed(args.seed)

    # Initialize wandb
    wandb.init(
        project="ctr-prediction-Amazon-baselines",
        name=f"DCNv2_lr{args.lr}_bs{args.batch_size}_emb{args.embed_size}_hidden{args.hidden_size}_wd{args.weight_decay}",
        config={
            "model": "DCNv2",
            "dataset": "Amazon",
            "batch_size": args.batch_size,
            "learning_rate": args.lr,
            "weight_decay": args.weight_decay,
            "num_epochs": args.num_epochs,
            "embed_size": args.embed_size,
            "hidden_size": args.hidden_size,
            "num_hidden_layers": args.num_hidden_layers,
            "num_cross_layers": args.num_cross_layers,
            "dropout": args.dropout,
            "seed": args.seed
        }
    )

    # Get configurations
    model_config, training_config = get_configs()

    # Update configs with command line arguments
    model_config.embed_size = args.embed_size
    model_config.hidden_size = args.hidden_size
    model_config.num_hidden_layers = args.num_hidden_layers
    model_config.num_cross_layers = args.num_cross_layers
    model_config.hidden_dropout_rate = args.dropout

    training_config.batch_size = args.batch_size
    training_config.eval_batch_size = args.eval_batch_size
    training_config.learning_rate = args.lr
    training_config.weight_decay = args.weight_decay
    training_config.num_epochs = args.num_epochs
    training_config.data_dir = args.data_dir
    training_config.output_dir = args.output_dir

    # Create output directory
    os.makedirs(training_config.output_dir, exist_ok=True)

    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Get data loaders
    meta_data_path = os.path.join(training_config.data_dir, training_config.meta_data_file)
    h5_data_path = os.path.join(training_config.data_dir, training_config.h5_data_file)

    train_loader, valid_loader, test_loader = get_data_loaders(
        training_config,
        meta_data_path,
        h5_data_path
    )

    # Load meta data to get input dimensions
    with open(meta_data_path, 'r') as f:
        meta_data = json.load(f)

    model_config.num_fields = len(meta_data['field_names'])
    model_config.input_size = sum(meta_data['feature_count'])

    logger.info(f"Number of fields: {model_config.num_fields}")
    logger.info(f"Input size: {model_config.input_size}")
    logger.info(f"Field names: {meta_data['field_names']}")

    # Create model
    model = DCNv2(model_config).to(device)
    logger.info(f"Model created with {sum(p.numel() for p in model.parameters())} parameters")

    # Define loss function and optimizer
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.Adam(model.parameters(), lr=training_config.learning_rate, weight_decay=training_config.weight_decay)

    # Training loop
    logger.info("Starting training...")
    best_auc = 0
    best_epoch = 0

    for epoch in range(training_config.num_epochs):
        # Train
        start_time = time.time()
        train_loss = train_epoch(model, train_loader, optimizer, criterion, device)
        train_time = time.time() - start_time

        # Evaluate on validation set
        valid_metrics = evaluate_model(model, valid_loader, device)

        # Evaluate on test set
        test_metrics = evaluate_model(model, test_loader, device)

        # Log results
        logger.info(f"Epoch {epoch+1}/{training_config.num_epochs} - "
                   f"Train Loss: {train_loss:.4f} - "
                   f"Valid AUC: {valid_metrics['auc']:.4f} - "
                   f"Valid LogLoss: {valid_metrics['logloss']:.4f} - "
                   f"Test AUC: {test_metrics['auc']:.4f} - "
                   f"Test LogLoss: {test_metrics['logloss']:.4f} - "
                   f"Time: {train_time:.2f}s")

        # Log to wandb
        wandb.log({
            "epoch": epoch + 1,
            "train_loss": train_loss,
            "valid_auc": valid_metrics['auc'],
            "valid_logloss": valid_metrics['logloss'],
            "valid_accuracy": valid_metrics['accuracy'],
            "test_auc": test_metrics['auc'],
            "test_logloss": test_metrics['logloss'],
            "test_accuracy": test_metrics['accuracy'],
            "train_time": train_time
        })

        # Save best model based on validation AUC
        if valid_metrics['auc'] > best_auc:
            best_auc = valid_metrics['auc']
            best_epoch = epoch + 1

            # Save model
            model_path = os.path.join(training_config.output_dir, 'best_model.pth')
            torch.save(model.state_dict(), model_path)

            # Save configs
            model_config.save(training_config.output_dir)
            training_config.save(training_config.output_dir)

            logger.info(f"New best model saved with validation AUC: {best_auc:.4f}")

    # Final evaluation on test set
    model.load_state_dict(torch.load(os.path.join(training_config.output_dir, 'best_model.pth')))
    final_test_metrics = evaluate_model(model, test_loader, device)

    logger.info(f"Training completed. Best validation AUC: {best_auc:.4f} at epoch {best_epoch}")
    logger.info(f"Final test metrics - AUC: {final_test_metrics['auc']:.4f}, "
               f"LogLoss: {final_test_metrics['logloss']:.4f}, "
               f"Accuracy: {final_test_metrics['accuracy']:.4f}")

    # Save final results
    results = {
        'best_validation_auc': float(best_auc),
        'best_epoch': int(best_epoch),
        'final_test_auc': float(final_test_metrics['auc']),
        'final_test_logloss': float(final_test_metrics['logloss']),
        'final_test_accuracy': float(final_test_metrics['accuracy'])
    }

    # Log final results to wandb
    wandb.log({
        "best_validation_auc": best_auc,
        "best_epoch": best_epoch,
        "final_test_auc": final_test_metrics['auc'],
        "final_test_logloss": final_test_metrics['logloss'],
        "final_test_accuracy": final_test_metrics['accuracy']
    })

    # Log summary metrics
    wandb.run.summary["best_validation_auc"] = best_auc
    wandb.run.summary["best_epoch"] = best_epoch
    wandb.run.summary["final_test_auc"] = final_test_metrics['auc']
    wandb.run.summary["final_test_logloss"] = final_test_metrics['logloss']
    wandb.run.summary["final_test_accuracy"] = final_test_metrics['accuracy']

    results_path = os.path.join(training_config.output_dir, 'results.json')
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"Results saved to {results_path}")

    # Finish wandb run
    wandb.finish()


if __name__ == "__main__":
    main()
