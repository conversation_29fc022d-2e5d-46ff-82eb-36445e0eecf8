#!/usr/bin/env python3
"""
Train UniSRec model on Amazon dataset for comparative study
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime

# Add UniSRec to path
sys.path.append('Unisrec')

def prepare_amazon_data_for_unisrec():
    """Prepare Amazon data for UniSRec training"""
    print("📊 Preparing Amazon data for UniSRec...")
    
    # Load the Amazon data with split categories
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')
    
    print(f"Train shape: {train_df.shape}")
    print(f"Val shape: {val_df.shape}")
    print(f"Test shape: {test_df.shape}")
    
    # Create output directory
    output_dir = 'Unisrec/dataset/downstream/Amazon'
    os.makedirs(output_dir, exist_ok=True)
    
    # UniSRec expects sequential recommendation format
    # Convert to UniSRec format: user_id, item_sequence, target_item, label
    
    def create_unisrec_sequences(df, min_seq_len=3):
        """Create sequences for UniSRec"""
        # Group by user to create sequences
        user_sequences = []
        
        for user_id, user_data in df.groupby('user_id'):
            # Sort by some order (using index as proxy for time)
            user_data = user_data.sort_index()
            
            items = user_data['item_id'].tolist()
            labels = user_data['label'].tolist()
            
            # Create sequences of minimum length
            if len(items) >= min_seq_len:
                for i in range(min_seq_len-1, len(items)):
                    sequence = items[:i]
                    target_item = items[i]
                    target_label = labels[i]
                    
                    user_sequences.append({
                        'user_id': user_id,
                        'item_sequence': sequence,
                        'target_item': target_item,
                        'label': target_label
                    })
        
        return pd.DataFrame(user_sequences)
    
    # Convert to UniSRec format
    train_unisrec = create_unisrec_sequences(train_df)
    val_unisrec = create_unisrec_sequences(val_df)
    test_unisrec = create_unisrec_sequences(test_df)
    
    # Save UniSRec format data
    train_unisrec.to_csv(f'{output_dir}/train.csv', index=False)
    val_unisrec.to_csv(f'{output_dir}/val.csv', index=False)
    test_unisrec.to_csv(f'{output_dir}/test.csv', index=False)
    
    # Create UniSRec dataset info
    dataset_info = {
        'dataset_name': 'Amazon',
        'num_users': max(train_df['user_id'].max(), val_df['user_id'].max(), test_df['user_id'].max()) + 1,
        'num_items': max(train_df['item_id'].max(), val_df['item_id'].max(), test_df['item_id'].max()) + 1,
        'num_sequences': len(train_unisrec) + len(val_unisrec) + len(test_unisrec),
        'task': 'sequential_recommendation'
    }
    
    with open(f'{output_dir}/info.json', 'w') as f:
        json.dump(dataset_info, f, indent=2)
    
    print(f"✅ UniSRec data saved to {output_dir}")
    print(f"📊 Dataset info: {dataset_info}")
    
    return output_dir

def train_amazon_unisrec():
    """Train UniSRec on Amazon dataset"""
    print("🚀 Starting UniSRec training on Amazon dataset...")
    
    # Prepare data
    data_dir = prepare_amazon_data_for_unisrec()
    if data_dir is None:
        print("❌ Data preparation failed!")
        return
    
    # Import UniSRec modules
    try:
        from Unisrec.run_custom_unisrec import run_unisrec_training
        from Unisrec.config import Config
    except ImportError as e:
        print(f"❌ Failed to import UniSRec modules: {e}")
        return None
    
    # Configure UniSRec parameters
    config = Config()
    config.dataset = 'Amazon'
    config.data_path = data_dir
    config.output_dir = 'Unisrec/output_amazon_unisrec'
    config.epochs = 10
    config.batch_size = 256
    config.learning_rate = 0.001
    config.embedding_size = 64
    config.hidden_size = 128
    config.num_layers = 2
    config.dropout = 0.2
    config.seed = 2023
    config.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    config.task = 'ctr_prediction'  # Adapt for CTR task
    
    # Train the model
    try:
        # Run UniSRec training
        results = run_unisrec_training(config)
        
        # Save results
        output_dir = config.output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        results_dict = {
            'model': 'UniSRec',
            'dataset': 'Amazon',
            'timestamp': datetime.now().isoformat(),
            'test_auc': results.get('test_auc', 0.0),
            'test_logloss': results.get('test_logloss', 0.0),
            'test_accuracy': results.get('test_accuracy', 0.0),
            'task': 'ctr_prediction'
        }
        
        with open(f'{output_dir}/results.json', 'w') as f:
            json.dump(results_dict, f, indent=2)
        
        print("✅ UniSRec training completed!")
        print(f"📊 Results: AUC={results_dict['test_auc']:.4f}, "
              f"Logloss={results_dict['test_logloss']:.4f}")
        
        return results_dict
        
    except Exception as e:
        print(f"❌ UniSRec training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    train_amazon_unisrec()
