#!/usr/bin/env python3
"""
Train DCNv2 model on Amazon dataset for comparative study
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime

# Add DCNv2 to path
sys.path.append('GraphLLM4CTR_baseline/DCNv2')

from GraphLLM4CTR_baseline.DCNv2.train import main as dcnv2_main
from GraphLLM4CTR_baseline.DCNv2.config import get_configs

def prepare_amazon_data():
    """Prepare Amazon data for DCNv2 training"""
    print("📊 Preparing Amazon data for DCNv2...")

    # Load the Amazon data with split categories
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')

    print(f"Train shape: {train_df.shape}")
    print(f"Val shape: {val_df.shape}")
    print(f"Test shape: {test_df.shape}")

    # DCNv2 expects specific column format
    # Map Amazon columns to DCNv2 expected format
    feature_columns = ['user_id', 'item_id', 'brand_index', 'price_range_index',
                      'category_1', 'category_2', 'category_3', 'price']

    # Ensure all feature columns are present
    for col in feature_columns:
        if col not in train_df.columns:
            print(f"❌ Missing column: {col}")
            return None

    # Save processed data for DCNv2
    output_dir = 'GraphLLM4CTR_baseline/DCNv2/data/amazon'
    os.makedirs(output_dir, exist_ok=True)

    train_df[feature_columns + ['label']].to_csv(f'{output_dir}/train.csv', index=False)
    val_df[feature_columns + ['label']].to_csv(f'{output_dir}/val.csv', index=False)
    test_df[feature_columns + ['label']].to_csv(f'{output_dir}/test.csv', index=False)

    print(f"✅ Data saved to {output_dir}")
    return output_dir

def train_amazon_dcnv2():
    """Train DCNv2 on Amazon dataset"""
    print("🚀 Starting DCNv2 training on Amazon dataset...")

    # Prepare data
    data_dir = prepare_amazon_data()
    if data_dir is None:
        print("❌ Data preparation failed!")
        return

    # Use existing DCNv2 training script for BookCrossing as template
    try:
        # Import the existing BookCrossing DCNv2 training
        from GraphLLM4CTR_baseline.DCNv2.train_bookcrossing import train_dcnv2_bookcrossing

        # Modify for Amazon dataset
        results = train_dcnv2_bookcrossing(
            data_dir=data_dir,
            output_dir="GraphLLM4CTR_baseline/DCNv2/output_amazon_dcnv2",
            dataset_name="Amazon"
        )

        print("✅ DCNv2 training completed!")
        print(f"📊 Results: AUC={results.get('test_auc', 'N/A'):.4f}, "
              f"Logloss={results.get('test_logloss', 'N/A'):.4f}")

        return results

    except Exception as e:
        print(f"❌ DCNv2 training failed: {e}")
        import traceback
        traceback.print_exc()

        # Return a placeholder result to continue with other models
        return {
            'model': 'DCNv2',
            'dataset': 'Amazon',
            'status': 'failed',
            'error': str(e),
            'test_auc': 0.0,
            'test_logloss': 1.0
        }

if __name__ == "__main__":
    train_amazon_dcnv2()
