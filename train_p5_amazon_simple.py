#!/usr/bin/env python3
"""
Simple P5 training script for Amazon dataset
"""

import os
import sys
import subprocess
import json
import pandas as pd
from datetime import datetime

def prepare_amazon_data_for_p5():
    """Prepare Amazon data in P5 format"""
    print("📊 Preparing Amazon data for P5...")

    # Load Amazon data
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')

    # Create P5 output directory
    p5_data_dir = 'GraphLLM4CTR_baseline/P5/data/amazon'
    os.makedirs(p5_data_dir, exist_ok=True)

    def create_p5_format(df, split_name):
        """Convert to P5 text format"""
        p5_data = []

        for _, row in df.iterrows():
            # Create text prompt for P5
            user_id = row['user_id']
            item_id = row['item_id']
            brand = row['brand_index']
            price_range = row['price_range_index']
            cat1, cat2, cat3 = row['category_1'], row['category_2'], row['category_3']
            label = row['label']

            # Create descriptive text
            item_desc = f"item {item_id} from brand {brand} in categories {cat1}-{cat2}-{cat3} with price range {price_range}"

            # P5 prompt format
            prompt = f"Will user {user_id} interact with {item_desc}?"
            target = "yes" if label == 1 else "no"

            p5_data.append({
                'user_id': user_id,
                'item_id': item_id,
                'prompt': prompt,
                'target': target,
                'label': label
            })

        # Save P5 format
        p5_df = pd.DataFrame(p5_data)
        p5_df.to_csv(f'{p5_data_dir}/{split_name}.csv', index=False)
        return p5_df

    # Convert all splits
    train_p5 = create_p5_format(train_df, 'train')
    val_p5 = create_p5_format(val_df, 'val')
    test_p5 = create_p5_format(test_df, 'test')

    # Create dataset info (convert numpy types to Python types)
    dataset_info = {
        'dataset': 'amazon',
        'num_users': int(max(train_df['user_id'].max(), val_df['user_id'].max(), test_df['user_id'].max()) + 1),
        'num_items': int(max(train_df['item_id'].max(), val_df['item_id'].max(), test_df['item_id'].max()) + 1),
        'train_samples': int(len(train_p5)),
        'val_samples': int(len(val_p5)),
        'test_samples': int(len(test_p5))
    }

    with open(f'{p5_data_dir}/info.json', 'w') as f:
        json.dump(dataset_info, f, indent=2)

    print(f"✅ P5 data prepared: {dataset_info}")
    return p5_data_dir

def train_p5_amazon():
    """Train P5 on Amazon dataset"""
    print("🚀 Starting P5 training on Amazon dataset...")

    # Prepare data
    data_dir = prepare_amazon_data_for_p5()

    # Create a simple P5 training script
    p5_script = f"""
import sys
sys.path.append('/data/p5_model')

import pandas as pd
import torch
from transformers import T5Tokenizer, T5ForConditionalGeneration
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, log_loss
import numpy as np
from tqdm import tqdm

class AmazonP5Dataset(Dataset):
    def __init__(self, csv_file, tokenizer, max_length=128):
        self.data = pd.read_csv(csv_file)
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        prompt = row['prompt']
        target = row['target']
        label = row['label']

        # Tokenize
        inputs = self.tokenizer(prompt, max_length=self.max_length,
                               padding='max_length', truncation=True, return_tensors='pt')
        targets = self.tokenizer(target, max_length=10,
                                padding='max_length', truncation=True, return_tensors='pt')

        return {{
            'input_ids': inputs.input_ids.squeeze(),
            'attention_mask': inputs.attention_mask.squeeze(),
            'labels': targets.input_ids.squeeze(),
            'raw_label': torch.tensor(label, dtype=torch.float)
        }}

# Load tokenizer and model
tokenizer = T5Tokenizer.from_pretrained('/data/p5_model')
model = T5ForConditionalGeneration.from_pretrained('/data/p5_model')

# Load data
train_dataset = AmazonP5Dataset('{data_dir}/train.csv', tokenizer)
val_dataset = AmazonP5Dataset('{data_dir}/val.csv', tokenizer)
test_dataset = AmazonP5Dataset('{data_dir}/test.csv', tokenizer)

# Create dataloaders
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=16)
test_loader = DataLoader(test_dataset, batch_size=16)

# Simple training loop
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)
optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)

print("Training P5 on Amazon...")
for epoch in range(3):
    model.train()
    total_loss = 0

    for batch in tqdm(train_loader, desc=f"Epoch {{epoch+1}}"):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        labels = batch['labels'].to(device)

        outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
        loss = outputs.loss

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        total_loss += loss.item()

    print(f"Epoch {{epoch+1}} Loss: {{total_loss/len(train_loader):.4f}}")

# Simple evaluation
model.eval()
all_preds = []
all_labels = []

with torch.no_grad():
    for batch in tqdm(test_loader, desc="Evaluating"):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        raw_labels = batch['raw_label'].numpy()

        # Generate predictions
        outputs = model.generate(input_ids=input_ids, attention_mask=attention_mask,
                                max_length=10, num_return_sequences=1)

        # Decode predictions
        for i, output in enumerate(outputs):
            pred_text = tokenizer.decode(output, skip_special_tokens=True).strip().lower()
            pred_prob = 0.7 if 'yes' in pred_text else 0.3  # Simple heuristic
            all_preds.append(pred_prob)
            all_labels.append(raw_labels[i])

# Calculate metrics
all_preds = np.array(all_preds)
all_labels = np.array(all_labels)
auc = roc_auc_score(all_labels, all_preds)
logloss = log_loss(all_labels, all_preds)

print(f"P5 Amazon Results:")
print(f"Test AUC: {{auc:.4f}}")
print(f"Test Logloss: {{logloss:.4f}}")

# Save results
results = {{
    'model': 'P5',
    'dataset': 'Amazon',
    'test_auc': float(auc),
    'test_logloss': float(logloss)
}}

import json
with open('GraphLLM4CTR_baseline/P5/amazon_results.json', 'w') as f:
    json.dump(results, f, indent=2)
"""

    # Write and run the script
    script_path = 'GraphLLM4CTR_baseline/P5/run_amazon_p5.py'
    with open(script_path, 'w') as f:
        f.write(p5_script)

    # Run P5 training
    try:
        result = subprocess.run(f'cd GraphLLM4CTR_baseline/P5 && python run_amazon_p5.py',
                               shell=True, capture_output=True, text=True, timeout=1800)

        if result.returncode == 0:
            print("✅ P5 training completed!")
            print(result.stdout[-500:])  # Last 500 chars

            # Load results
            try:
                with open('GraphLLM4CTR_baseline/P5/amazon_results.json', 'r') as f:
                    results = json.load(f)
                return results
            except:
                return {'model': 'P5', 'status': 'completed', 'note': 'Results file not found'}
        else:
            print("❌ P5 training failed!")
            print(result.stderr[-500:])
            return {'model': 'P5', 'status': 'failed', 'error': result.stderr[-200:]}

    except subprocess.TimeoutExpired:
        print("⏰ P5 training timed out!")
        return {'model': 'P5', 'status': 'timeout'}
    except Exception as e:
        print(f"❌ P5 training error: {e}")
        return {'model': 'P5', 'status': 'error', 'error': str(e)}

if __name__ == "__main__":
    result = train_p5_amazon()
    print(f"P5 Result: {result}")
